/* eslint-disable no-underscore-dangle */
import globals from 'globals';
import path from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';
import pluginJs from '@eslint/js';

// mimic CommonJS variables -- not needed if using CommonJS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: pluginJs.configs.recommended,
});

export default [
  ...compat.extends('airbnb-base'),
  {
    languageOptions: {
      globals: globals.node,
      ecmaVersion: 2020, // Ensure ES2020 for optional chaining
      sourceType: 'module', // Ensure ES Modules (important for imports/exports)
    },
    rules: {
      'no-param-reassign': [2, { props: false }],
      'linebreak-style': ['error', 'unix'],
      'import/extensions': [
        'error',
        {
          js: 'ignorePackages',
        },
      ],
      'import/no-extraneous-dependencies': [
        'error',
        {
          devDependencies: ['eslint.config.mjs'],
        },
      ],
      indent: ['error'],
      'max-len': ['error', { code: 150 }],
      'import/prefer-default-export': 'off',
    },
  },
];
