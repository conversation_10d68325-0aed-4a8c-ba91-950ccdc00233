import { log } from '../logging/logger.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import { generateRandomHashedPassword } from '../utils/passwordUtil.js';
import { DEFAULT_LANGUAGE } from '../constants/ELanguage.js';
import { ESubRole } from '../constants/ESubRole.js';
import { ERole } from '../constants/ERole.js';
import * as EmailService from '../services/emailService.js';
import { addSubRoleOfUser } from '../services/roleService.js';

export const defaultUserDataLoader = async () => {
  if (process.env.DEFAULT_USER_EMAIL) {
    const defaultUser = await UserRepository.findByEmail(process.env.DEFAULT_USER_EMAIL);
    if (!defaultUser) {
      const user = await UserRepository.createUser(
        process.env.DEFAULT_USER_EMAIL,
        'Default Admin',
        '', (
          await generateRandomHashedPassword()),
        DEFAULT_LANGUAGE,
      );
      await addSubRoleOfUser(user.id, ESubRole.ADMIN, ERole.INTERNAL_USER);

      await EmailService.sendAccountValidationEmail(user.id, DEFAULT_LANGUAGE, process.env.VALIDATION_TYPE);
      log('info', 'Default user created');
    }
  } else {
    log('info', 'DEFAULT_USER_EMAIL is not defined in .env. Default user will not be created');
  }
};
