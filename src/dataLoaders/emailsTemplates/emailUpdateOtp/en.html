<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8"> <!-- xxx utf-8 works for most cases -->
    <meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldn't be necessary -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
    <meta name="x-apple-disable-message-reformatting"> <!-- Disable auto-scale in iOS 10 Mail entirely -->
    <link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'>
    <!-- The title tag shows in email notifications, like Android 4.4. -->
    <!-- Web Font / @font-face : BEGIN -->
    <!-- NOTE: If web fonts are not required, lines 10 - 27 can be safely removed. -->
    <!-- Desktop Outlook chokes on web font references and defaults to Times New Roman, so we force a safe fallback font. -->
    <!--[if mso]>    <style>            * {        font-family: sans-serif !important;    }        </style>    <![endif]-->
    <!-- All other clients get the webfont reference; some will render the font and others will silently fail to the fallbacks. -->
    <!--[if !mso]><!-->
    <!--<link href='https://fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'>-->
    <!--<![endif]-->
    <!-- Web Font / @font-face : END -->
    <!-- CSS Reset : BEGIN -->
    <style>
        /* What it does: Remove spaces around the email design added by some email clients. */
        /* Beware: It can remove the padding / margin and add a background color to the compose a reply window. */
        html,
        body {
            margin: 0 auto !important;
            padding: 0 !important;
            height: 100% !important;
            width: 100% !important;
        }

        /* What it does: Stops email clients resizing small text. */
        * {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }

        /* What it does: Centers email on Android 4.4 */
        div[style*="margin: 16px 0"] {
            margin: 0 !important;
        }

        /* What it does: Stops Outlook from adding extra spacing to tables. */
        table,
        td {
            mso-table-lspace: 0pt !important;
            mso-table-rspace: 0pt !important;
        }

        /* What it does: Fixes webkit padding issue. */
        table {
            border-spacing: 0 !important;
            border-collapse: collapse !important;
            table-layout: fixed !important;
            margin: 0 auto !important;
        }

        /* What it does: Prevents Windows 10 Mail from underlining links despite inline CSS. Styles for underlined links should be inline. */
        a {
            text-decoration: none;
        }

        /* What it does: Uses a better rendering method when resizing images in IE. */
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* What it does: A work-around for email clients meddling in triggered links. */
        a[x-apple-data-detectors],
        /* iOS */
        .unstyle-auto-detected-links a,
        .aBn {
            border-bottom: 0 !important;
            cursor: default !important;
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        /* What it does: Prevents Gmail from changing the text color in conversation threads. */
        .im {
            color: inherit !important;
        }

        /* What it does: Prevents Gmail from displaying a download button on large, non-linked images. */
        .a6S {
            display: none !important;
            opacity: 0.01 !important;
        }

        /* If the above doesn't work, add a .g-img class to any image in question. */
        img.g-img+div {
            display: none !important;
        }

        /* What it does: Removes right gutter in Gmail iOS app */
        /* Create one of these media queries for each additional viewport size you'd like to fix */
        /* iPhone 4, 4S, 5, 5S, 5C, and 5SE */
        @media only screen and (min-device-width: 320px) and (max-device-width: 374px) {
            u~div .email-container {
                min-width: 320px !important;
            }
        }

        /* iPhone 6, 6S, 7, 8, and X */
        @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
            u~div .email-container {
                min-width: 375px !important;
            }
        }

        /* iPhone 6+, 7+, and 8+ */
        @media only screen and (min-device-width: 414px) {
            u~div .email-container {
                min-width: 414px !important;
            }
        }
    </style> <!-- What it does: Makes background images in 72ppi Outlook render at correct size. -->
    <!--[if gte mso 9]>    <xml>        <o:OfficeDocumentSettings>            <o:AllowPNG/>            <o:PixelsPerInch>96</o:PixelsPerInch>        </o:OfficeDocumentSettings>    </xml>    <![endif]-->
    <!-- CSS Reset : END -->
    <!-- Progressive Enhancements : BEGIN -->
    <style>
        /* What it does: Hover styles for buttons */
        .button-td,
        .button-a {
            transition: all 100ms ease-in;
        }

        .button-td-primary:hover,
        .button-a-primary:hover {
            background: #656975 !important;
            border-color: #656975 !important;
        }

        /* Media Queries */
        @media screen and (max-width: 575px) {
            .email-container {
                width: 100% !important;
                margin: auto !important;
            }

            /* What it does: Forces table cells into full-width rows. */
            .stack-column,
            .stack-column-center {
                display: block !important;
                width: 100% !important;
                max-width: 100% !important;
                direction: ltr !important;
            }

            /* And center justify these ones. */
            .stack-column-center {
                text-align: center !important;
            }

            /* What it does: Generic utility class for centering. Useful for images, buttons, and nested tables. */
            .center-on-narrow {
                text-align: center !important;
                display: block !important;
                margin-left: auto !important;
                margin-right: auto !important;
                float: none !important;
            }

            table.center-on-narrow {
                display: inline-block !important;
            }

            /* What it does: Adjust typography on small screens to improve readability */
            .email-container p {
                font-size: 14px !important;
            }

            .mobile-title {
                font-size: 20px !important;
                line-height: 1.5 !important;
            }
        }
    </style> <!-- Progressive Enhancements : END -->
</head>
<!--     The email background color (#f6f6f6) is defined in three places:  
1. body tag: for most email clients                
2. center tag: for Gmail and Inbox mobile apps and web versions of Gmail, GSuite, Inbox, Yahoo, AOL, Libero, Comcast, freenet, Mail.ru, Orange.fr       
3. mso conditional: For Windows 10 Mail-->

<body width="100%" style="margin: 0; padding: 0 !important; mso-line-height-rule: exactly; background-color: #f6f6f6;">
    <center style="width: 100%; background-color: #f6f6f6;">
        <!--[if mso | IE]>    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f6f6f6;">        <tr>            <td>    <![endif]-->
        <!-- Create white space after the desired preview text so email clients don?t pull other distracting text into the inbox preview. Extend as necessary. -->
        <!-- Email Body : BEGIN -->
        <table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" width="575" style="margin: auto;" class="email-container">
            <tr>
                <td style="padding: 0 16px 0;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <!-- Email Header : BEGIN -->
                        <tr>
                            <td style="padding: 48px 0 0; text-align: center; "><img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRzWoFYI8lWlenqMaIm-7qkuQ0viqk1l4orGQ&s"  title="© Caixa Mágica Software" alt="© Caixa Mágica Software" border="0" style="width: 100%; max-width: 150px; font-family: 'Roboto', sans-serif; font-size: 12px; line-height: 1.5; color: #656975;"> </td>
                        </tr> <!-- Email Header : END -->
                        <!-- 1 Column Text + Button : BEGIN -->
                        <tr>
                            <td style="background-color: #ffffff; padding: 32px 32px 24px; border-top: 1px solid #eaeaea; border-left: 1px solid #eaeaea; border-right: 1px solid #eaeaea; border-bottom: 1px solid #eaeaea;">
                                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                    <!--<tr>
                                        <td style="font-family: 'Roboto', sans-serif; padding: 0 0 4px; text-align: left;  font-size: 12px; line-height: 1.5;"><span style="color: #656975;">Data: </span> <span style="color: #656975; font-weight: bold;">
                                                <!todaysDate!>
                                            </span></td>
                                    </tr>-->
                                    <tr>
                                        <td style="padding: 0px; font-family: 'Roboto', sans-serif; text-align: left; font-size: 12px; line-height: 1.5; color: #656975;">
                                            <p>Caixa Mágica Software</p>
                                            <p>Your update email code is: </p>
                                        </td>
                                    </tr> <!-- 2 Even Columns : BEGIN -->
                                    <tr>
                                        <td style="padding: 12px 32px 12px; background-color: #F6FBFF;">
                                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                                <tr>
                                                    <!-- Column : BEGIN -->
                                                    <td valign="top" width="100%" class="stack-column-center">
                                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                                            <tr>
                                                                <td style="font-family: 'Roboto', sans-serif; font-size: 24px; line-height: 1.5; color: #1F66B0; padding: 0; text-align: center;" class="center-on-narrow">
                                                                    <!email_update_otp!>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td> <!-- Column : END -->
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td style="padding: 0px; font-family: 'Roboto', sans-serif; text-align: left; font-size: 12px; line-height: 1.5; color: #656975;">
                                            <p> Kind regards, </p>
                                            <p>Your team
                                            </p>
                                            <p><img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRzWoFYI8lWlenqMaIm-7qkuQ0viqk1l4orGQ&s"  title="© Caixa Mágica Software" alt="© Caixa Mágica Software" border="0" style="width: 100%; max-width: 100px; font-family: 'Roboto', sans-serif; font-size: 12px; line-height: 1.5; color: #656975;"> </p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr> <!-- 1 Column Text + Button : END -->
                        <!-- Clear Spacer : BEGIN -->
                        <tr>
                            <td aria-hidden="true" height="16" style="font-size: 0px; line-height: 0px;"> &nbsp;</td>
                        </tr> <!-- Clear Spacer : END -->
                        <!-- Clear Spacer : BEGIN -->
                        <tr>
                            <td aria-hidden="true" height="16" style="font-size: 0px; line-height: 0px;"> &nbsp;</td>
                        </tr> <!-- Clear Spacer : END -->
                        <!-- Clear Spacer : BEGIN -->
                        <tr>
                            <td aria-hidden="true" height="16" style="font-size: 0px; line-height: 0px;"> &nbsp;</td>
                        </tr> <!-- Clear Spacer : END -->
                    </table>
                </td>
            </tr>
        </table> <!-- Email Body : END -->
        <!-- Email Footer : BEGIN -->
        <table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" width="575" style="margin: auto;" class="email-container">
            <tr>
                <td style="padding: 24px 0 0; text-align: center; font-family: 'Roboto', sans-serif; font-size: 12px; line-height: 1.5; color: #656975;"> © Caixa Mágica Software</td>
            </tr>
            <tr>
                <td style="padding: 8px 32px 48px; text-align: center; font-family: 'Roboto', sans-serif; font-size: 12px; line-height: 1.5; color: #656975;"><span style="margin: 0;">
                       
                    </span></td>
            </tr>
        </table> <!-- Email Footer : END -->
        <!--[if mso | IE]>    </td>    </tr>    </table>    <![endif]-->
    </center>
</body>

</html>