import { ERole } from '../constants/ERole.js';
import * as RoleRepository from '../database/repositories/roleRepository.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import * as FunctionalityRepository from '../database/repositories/functionalityRepository.js';
import { ESubRole } from '../constants/ESubRole.js';
import * as SubRoleRepository from '../database/repositories/subRoleRepository.js';
import * as SubRoleFunctionalityRepository from '../database/repositories/subRoleFunctionalityRepository.js';
import { log } from '../logging/logger.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import { EPermissionValue } from '../constants/EPermissionValue.js';

const createSubRoleIfNotExists = async (subRoleCode, roleCode) => {
  const role = await RoleRepository.findByCode(roleCode);
  const subRole = await SubRoleRepository.findBySubRoleCodeAndRoleId(subRoleCode, role.id);
  if (!subRole) {
    await SubRoleRepository.createSubRole(subRoleCode, role.id);
  }
};

const createSubRoleFunctionalityIfNotExists = async (roleCode, subRoleCode, functionalityCode, permissionAction, permissionValue) => {
  const role = await RoleRepository.findByCode(roleCode);
  const subRole = await SubRoleRepository.findBySubRoleCodeAndRoleId(subRoleCode, role.id);
  const functionality = await FunctionalityRepository.findByCode(functionalityCode.code);
  const subRoleFunctionality = await SubRoleFunctionalityRepository.findByFunctionalityIdAndSubRoleIdPermissions(
    functionality.id,
    subRole.id,
    permissionAction,
    permissionValue,
  );

  if (!subRoleFunctionality) {
    await SubRoleFunctionalityRepository.createSubRoleFunctionality(subRole.id, functionality.id, permissionAction, permissionValue);
  }
};

export const roleDataLoader = async () => {
  // Create all roles in ERole
  await Promise.all(
    Object.values(ERole).map(async (roleCode) => {
      const exists = await RoleRepository.findByCode(roleCode);
      if (!exists) {
        await RoleRepository.createRole(roleCode);
      }
    }),
  );

  // Create subroles
  // Internal users
  await createSubRoleIfNotExists(ESubRole.ADMIN, ERole.INTERNAL_USER);
  await createSubRoleIfNotExists(ESubRole.CLIENT_SUPPORT, ERole.INTERNAL_USER);
  // External users
  await createSubRoleIfNotExists(ESubRole.USER, ERole.EXTERNAL_USER);

  // Creates all functionalities in EFunctionality
  await Promise.all(
    Object.values(EFunctionality).map(async (functionality) => {
      const exists = await FunctionalityRepository.findByCode(functionality.code);
      if (!exists) {
        await FunctionalityRepository.createFunctionality(functionality.code, functionality.module);
      }
    }),
  );

  // Assign functionalities to subroles
  // INTERNAL_USER ADMIN
  // USER_MANAGEMENT

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // PERMISSION_MANAGEMENT

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // PASSWORD_RESET

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // METRICS_VIEW

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // CHOOSE_LANGUAGE

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // LANGUAGE_MANAGEMENT
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.LANGUAGE_MANAGEMENT,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.LANGUAGE_MANAGEMENT,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.LANGUAGE_MANAGEMENT,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.ADMIN,
    EFunctionality.LANGUAGE_MANAGEMENT,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // INTERNAL_USER CLIENT_SUPPORT

  // PASSWORD_RESET
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // METRICS_VIEW
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // CHOOSE_LANGUAGE

  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.INTERNAL_USER,
    ESubRole.CLIENT_SUPPORT,
    EFunctionality.CHOOSE_LANGUAGE,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // EXTERNAL_USER USER

  // USER_MANAGEMENT

  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.USER_MANAGEMENT,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // PERMISSION_MANAGEMENT
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PERMISSION_MANAGEMENT,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );
  // PASSWORD_RESET
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.PASSWORD_RESET,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  // METRICS_VIEW

  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.CREATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.READ,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.UPDATE,
    EPermissionValue.ALL,
  );
  await createSubRoleFunctionalityIfNotExists(
    ERole.EXTERNAL_USER,
    ESubRole.USER,
    EFunctionality.METRICS_VIEW,
    EPermissionAction.DELETE,
    EPermissionValue.ALL,
  );

  log('info', 'Exiting method roleDataLoader');
};
