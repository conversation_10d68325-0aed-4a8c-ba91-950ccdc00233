/* eslint-disable no-underscore-dangle */
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import * as TranslationRepository from '../database/repositories/translationRepository.js';
import { ELanguage } from '../constants/ELanguage.js';
import { ERole } from '../constants/ERole.js';
import { EUserState } from '../constants/EUserState.js';
import { ESubRole } from '../constants/ESubRole.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { ETranslation } from '../constants/ETranslation.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Loads translations into the database.
// There should be a translation for each of the languages in ELanguage.
// There should also be a translation for each of the keys in ETranslation shown to the user
export const translationsDataLoader = async () => {
  // BE CONTROLLED ERRORS
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.FILE_NOT_FOUND, 'File not found.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.FILE_NOT_FOUND, 'Ficheiro não encontrado.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.FILE_NOT_FOUND, 'Plik nie został znaleziony.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.FILE_NOT_FOUND, 'Fișierul nu a fost găsit.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.UNEXPECTED_ERROR, 'An unexpected error occurred. Please contact support.');
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.UNEXPECTED_ERROR,
    'Ocorreu um erro inesperado. Por favor, entre em contato com o suporte.',
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.UNEXPECTED_ERROR,
    'Wystąpił nieoczekiwany błąd. Skontaktuj się z pomocą techniczną.',
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.UNEXPECTED_ERROR,
    'A apărut o eroare neașteptată. Vă rugăm să contactați suportul.',
  );

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.REQUEST_BODY_INVALID, 'Request body invalid');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.REQUEST_BODY_INVALID, 'Corpo do pedido inválido');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.REQUEST_BODY_INVALID, 'Nieprawidłowe body żądania');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.REQUEST_BODY_INVALID, 'Corp de cerere invalid');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.EMAIL_OR_PASSWORD_INCORRECT, 'Email or password incorrect.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.EMAIL_OR_PASSWORD_INCORRECT, 'Email ou palavra-passe incorretos.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.EMAIL_OR_PASSWORD_INCORRECT, 'Nieprawidłowy e-mail lub hasło.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.EMAIL_OR_PASSWORD_INCORRECT, 'Email sau parolă incorecte.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.MESSAGE_NOT_SENT, 'Message not sent, please try again.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.MESSAGE_NOT_SENT, 'Mensagem não enviada, por favor tente novamente.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.MESSAGE_NOT_SENT, 'Wiadomość nie została wysłana, spróbuj ponownie.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.MESSAGE_NOT_SENT, 'Mesajul nu a fost trimis, vă rugăm să încercați din nou.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.ROLE_NOT_FOUND, 'Role not found');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.ROLE_NOT_FOUND, 'Role não encontrado');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.ROLE_NOT_FOUND, 'Rola nie znaleziona');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.ROLE_NOT_FOUND, 'Rolă nu a fost găsită');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.SUBROLE_NOT_FOUND, 'SubRole not found');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.SUBROLE_NOT_FOUND, 'SubRole não encontrado');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.SUBROLE_NOT_FOUND, 'Podrola nie znaleziona');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.SUBROLE_NOT_FOUND, 'Subrolă nu a fost găsită');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.USER_ALREADY_EXISTS, 'User already exists.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.USER_ALREADY_EXISTS, 'Utilizador já existe.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.USER_ALREADY_EXISTS, 'Użytkownik już istnieje.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.USER_ALREADY_EXISTS, 'Utilizatorul există deja.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.OTP_VALIDATION_FAILED, 'Otp validation failed.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.OTP_VALIDATION_FAILED, 'Falha na validação do OTP.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.OTP_VALIDATION_FAILED, 'Weryfikacja OTP nie powiodła się.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.OTP_VALIDATION_FAILED, 'Validarea OTP a eșuat.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.USER_NOT_FOUND, 'User not found.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.USER_NOT_FOUND, 'Utilizador não encontrado.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.USER_NOT_FOUND, 'Użytkownik nie znaleziony.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.USER_NOT_FOUND, 'Utilizatorul nu a fost găsit.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.ACCOUNT_VALIDATION_FAILED, 'Account validation failed.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.ACCOUNT_VALIDATION_FAILED, 'Falha na validação da conta.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.ACCOUNT_VALIDATION_FAILED, 'Weryfikacja konta nie powiodła się.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.ACCOUNT_VALIDATION_FAILED, 'Validarea contului a eșuat.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.LANGUAGE_NOT_FOUND, 'Language not found.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.LANGUAGE_NOT_FOUND, 'Idioma não encontrado.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.LANGUAGE_NOT_FOUND, 'Język nie znaleziony.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.LANGUAGE_NOT_FOUND, 'Limba nu a fost găsită.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.UNABLE_TO_REGISTER, 'Unable to register.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.UNABLE_TO_REGISTER, 'Não foi possível registar.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.UNABLE_TO_REGISTER, 'Nie można się zarejestrować.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.UNABLE_TO_REGISTER, 'Înregistrarea a eșuat.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.INVALID_NEW_PASSWORD, 'A nova palavra-passe é inválida.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.INVALID_NEW_PASSWORD, 'The new password is invalid.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.INVALID_NEW_PASSWORD, 'Nowe hasło jest nieprawidłowe.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.INVALID_NEW_PASSWORD, 'Noua parolă este invalidă.');

  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.RESOURCE_ALREADY_EXISTS, 'O recurso já existe.');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.RESOURCE_ALREADY_EXISTS, 'Resource already exists.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.RESOURCE_ALREADY_EXISTS, 'Zasób już istnieje.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.RESOURCE_ALREADY_EXISTS, 'Resursa există deja.');

  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.MULTIPLE_CSV_FILES_UPLOADED, 'Vários ficheiros CSV foram carregados.');
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.MULTIPLE_CSV_FILES_UPLOADED, 'Multiple CSV files uploaded.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.MULTIPLE_CSV_FILES_UPLOADED, 'Przesłano wiele plików CSV.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.MULTIPLE_CSV_FILES_UPLOADED, 'Au fost încărcate mai multe fișiere CSV.');

  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.MISSING_CSV_FILE, 'Ficheiro CSV em falta.');
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.MISSING_CSV_FILE, 'Missing CSV file.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.MISSING_CSV_FILE, 'Brak pliku CSV.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.MISSING_CSV_FILE, 'Fișierul CSV lipsește.');

  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.USER_HAS_NO_PERMISSIONS, 'O utilizador não tem permissões.');
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.USER_HAS_NO_PERMISSIONS, 'User has no permissions.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.USER_HAS_NO_PERMISSIONS, 'Użytkownik nie ma uprawnień.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.USER_HAS_NO_PERMISSIONS, 'Utilizatorul nu are permisiuni.');

  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.PASSWORD_INCORRECT, 'A palavra-passe atual está incorreta.');
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.PASSWORD_INCORRECT, 'Current password is incorrect.');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.PASSWORD_INCORRECT, 'Obecne hasło jest nieprawidłowe.');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.PASSWORD_INCORRECT, 'Parola actuală este incorectă.');

  // EMAIL TEXTs

  // email login otp
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.EMAIL_SUBJECT_LOGIN_OTP, 'Login OTP');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.EMAIL_SUBJECT_LOGIN_OTP, 'OTP de Login');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.EMAIL_SUBJECT_LOGIN_OTP, 'Kod OTP do logowania');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.EMAIL_SUBJECT_LOGIN_OTP, 'OTP de autentificare');
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_LOGIN_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/login/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_LOGIN_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/login/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_LOGIN_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/login/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_LOGIN_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/login/ro.html'), 'utf-8'),
  );

  // email password recovery subject
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.EMAIL_SUBJECT_RECOVER_PASSWORD, 'Password recovery');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.EMAIL_SUBJECT_RECOVER_PASSWORD, 'Recuperação de palavra-passe');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.EMAIL_SUBJECT_RECOVER_PASSWORD, 'Odzyskiwanie hasła');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.EMAIL_SUBJECT_RECOVER_PASSWORD, 'Recuperare parolă');

  // email password recovery body with OTP
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordOtp/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordOtp/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordOtp/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordOtp/ro.html'), 'utf-8'),
  );

  // email password recovery body with token
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordToken/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordToken/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordToken/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/recoverPasswordToken/ro.html'), 'utf-8'),
  );

  // account validation subject
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.EMAIL_SUBJECT_ACCOUNT_VALIDATION, 'Account validation');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.EMAIL_SUBJECT_ACCOUNT_VALIDATION, 'Validação de conta');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.EMAIL_SUBJECT_ACCOUNT_VALIDATION, 'Walidacja konta');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.EMAIL_SUBJECT_ACCOUNT_VALIDATION, 'Validare cont');

  // account validation body with otp
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationOtp/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationOtp/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationOtp/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationOtp/ro.html'), 'utf-8'),
  );

  // account validation body with token
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationToken/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationToken/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationToken/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/accountValidationToken/ro.html'), 'utf-8'),
  );

  // email update subject
  await TranslationRepository.addTranslation(ELanguage.EN, ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, 'Update email');
  await TranslationRepository.addTranslation(ELanguage.PT, ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, 'Alterar email');
  await TranslationRepository.addTranslation(ELanguage.PL, ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, 'Aktualizuj email');
  await TranslationRepository.addTranslation(ELanguage.RO, ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, 'Actualizați e-mailul');

  // email update body with otp
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateOtp/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateOtp/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateOtp/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateOtp/ro.html'), 'utf-8'),
  );

  // email update body with token
  await TranslationRepository.addTranslation(
    ELanguage.EN,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateToken/en.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PT,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateToken/pt.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.PL,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateToken/pl.html'), 'utf-8'),
  );
  await TranslationRepository.addTranslation(
    ELanguage.RO,
    ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN,
    readFileSync(join(__dirname, 'emailsTemplates/emailUpdateToken/ro.html'), 'utf-8'),
  );

  // ROLES
  await TranslationRepository.addTranslation(ELanguage.EN, ERole.INTERNAL_USER, 'User ');
  await TranslationRepository.addTranslation(ELanguage.PT, ERole.INTERNAL_USER, 'Utilizador');
  await TranslationRepository.addTranslation(ELanguage.PL, ERole.INTERNAL_USER, 'Użytkownik');
  await TranslationRepository.addTranslation(ELanguage.RO, ERole.INTERNAL_USER, 'Utilizator');

  // SUB ROLES
  await TranslationRepository.addTranslation(ELanguage.EN, ESubRole.ADMIN, 'Administrator');
  await TranslationRepository.addTranslation(ELanguage.PT, ESubRole.ADMIN, 'Administrador');
  await TranslationRepository.addTranslation(ELanguage.PL, ESubRole.ADMIN, 'Administrator');
  await TranslationRepository.addTranslation(ELanguage.RO, ESubRole.ADMIN, 'Administrator');

  await TranslationRepository.addTranslation(ELanguage.EN, ESubRole.CLIENT_SUPPORT, 'Client Support');
  await TranslationRepository.addTranslation(ELanguage.PT, ESubRole.CLIENT_SUPPORT, 'Suporte ao Cliente');
  await TranslationRepository.addTranslation(ELanguage.PL, ESubRole.CLIENT_SUPPORT, 'Wsparcie Klienta');
  await TranslationRepository.addTranslation(ELanguage.RO, ESubRole.CLIENT_SUPPORT, 'Suport Client');

  await TranslationRepository.addTranslation(ELanguage.EN, ESubRole.USER, 'User');
  await TranslationRepository.addTranslation(ELanguage.PT, ESubRole.USER, 'Utilizador');
  await TranslationRepository.addTranslation(ELanguage.PL, ESubRole.USER, 'Użytkownik');
  await TranslationRepository.addTranslation(ELanguage.RO, ESubRole.USER, 'Utilizator');

  // USER STATES
  await TranslationRepository.addTranslation(ELanguage.EN, EUserState.ACTIVE, 'Active');
  await TranslationRepository.addTranslation(ELanguage.PT, EUserState.ACTIVE, 'Ativo');
  await TranslationRepository.addTranslation(ELanguage.PL, EUserState.ACTIVE, 'Aktywny');
  await TranslationRepository.addTranslation(ELanguage.RO, EUserState.ACTIVE, 'Activ');

  await TranslationRepository.addTranslation(ELanguage.EN, EUserState.BLOCKED, 'Blocked');
  await TranslationRepository.addTranslation(ELanguage.PT, EUserState.BLOCKED, 'Bloqueado');
  await TranslationRepository.addTranslation(ELanguage.PL, EUserState.BLOCKED, 'Zablokowany');
  await TranslationRepository.addTranslation(ELanguage.RO, EUserState.BLOCKED, 'Blocat');

  await TranslationRepository.addTranslation(ELanguage.EN, EUserState.PENDING_VALIDATION, 'Pending Validation');
  await TranslationRepository.addTranslation(ELanguage.PT, EUserState.PENDING_VALIDATION, 'Aguardando Validação');
  await TranslationRepository.addTranslation(ELanguage.PL, EUserState.PENDING_VALIDATION, 'Oczekuje na Walidację');
  await TranslationRepository.addTranslation(ELanguage.RO, EUserState.PENDING_VALIDATION, 'În Așteptarea Validării');

  // FUNCTIONALITIES
  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.USER_MANAGEMENT.code, 'User Management');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.USER_MANAGEMENT.code, 'Gestão de Utilizadores');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.USER_MANAGEMENT.code, 'Zarządzanie Użytkownikami');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.USER_MANAGEMENT.code, 'Gestionare Utilizatori');

  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.PERMISSION_MANAGEMENT.code, 'Permission Management');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.PERMISSION_MANAGEMENT.code, 'Gestão de Permissões');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.PERMISSION_MANAGEMENT.code, 'Zarządzanie Uprawnieniami');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.PERMISSION_MANAGEMENT.code, 'Gestionare Permisiuni');

  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.PASSWORD_RESET.code, 'Password Reset');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.PASSWORD_RESET.code, 'Redefinição de Palavra-passe');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.PASSWORD_RESET.code, 'Reset Hasła');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.PASSWORD_RESET.code, 'Resetare Parolă');

  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.METRICS_VIEW.code, 'Metrics View');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.METRICS_VIEW.code, 'Visualização de Métricas');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.METRICS_VIEW.code, 'Widok Metryk');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.METRICS_VIEW.code, 'Vizualizare Metrici');

  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.CHOOSE_LANGUAGE.code, 'Choose language');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.CHOOSE_LANGUAGE.code, 'Escolher idioma');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.CHOOSE_LANGUAGE.code, 'Wybierz język');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.CHOOSE_LANGUAGE.code, 'Alegeți limba');

  await TranslationRepository.addTranslation(ELanguage.EN, EFunctionality.LANGUAGE_MANAGEMENT.code, 'Language management');
  await TranslationRepository.addTranslation(ELanguage.PT, EFunctionality.LANGUAGE_MANAGEMENT.code, 'Gestão de idiomas');
  await TranslationRepository.addTranslation(ELanguage.PL, EFunctionality.LANGUAGE_MANAGEMENT.code, 'Zarządzanie językiem');
  await TranslationRepository.addTranslation(ELanguage.RO, EFunctionality.LANGUAGE_MANAGEMENT.code, 'Gestionarea limbajului');
};
