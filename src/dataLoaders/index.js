import { roleDataLoader } from './roleDataLoader.js';
import { languageDataLoader } from './languageDataLoader.js';
import { branchDataLoader } from './teamsDataLoader.js';
import { log } from '../logging/logger.js';
import { translationsDataLoader } from './translationsDataLoader.js';
import { defaultUserDataLoader } from './defaultUserDataLoader.js';
import { runWithTracking } from '../middlewares/requestMiddleware.js';

const loadDataWithTrackingId = async () => {
  await languageDataLoader();

  await roleDataLoader();
  await translationsDataLoader();

  await defaultUserDataLoader();

  await branchDataLoader();

  log('info', 'Exiting method loadData');
};

export const loadData = async () => {
  await runWithTracking(await loadDataWithTrackingId);
};
