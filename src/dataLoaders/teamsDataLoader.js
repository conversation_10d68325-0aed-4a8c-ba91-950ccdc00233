import { log } from '../logging/logger.js';
import * as TeamRepository from '../database/repositories/teamRepository.js';
import { EBranch } from '../constants/EBranch.js';

export const branchDataLoader = async () => {
  log('info', 'Entering method branchDataLoader');
  // Create all Branches in EBranch
  await Promise.all(
    Object.values(EBranch).map(async (branchName) => {
      const exists = await TeamRepository.findBranchByName(branchName);
      if (!exists) {
        await TeamRepository.createBranch(branchName);
      }
    }),
  );
};
