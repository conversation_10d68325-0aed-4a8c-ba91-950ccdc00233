import { log } from '../logging/logger.js';
import * as FileService from '../services/fileService.js';
import PaginationDTO from '../inputDTOs/paginationDTO.js';
import { ETranslation } from '../constants/ETranslation.js';
import FileFiltersDTO from '../inputDTOs/fileFiltersDTO.js';
import * as FileRepository from '../database/repositories/fileRepository.js';
import { convertFilesToDTO } from '../converters/fileConverter.js';
import UidDTO from '../inputDTOs/UidDTO.js';

export const uploadFile = async (req, res, next) => {
  try {
    // TODO: Validate entity depending on what it is. Could be used as id or string
    await FileService.uploadFile(req.files, req.body.entity);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const listFiles = async (req, res, next) => {
  try {
    // Combine PaginationDTO with FileFiltersDTO
    const { error, value: validatedParams } = PaginationDTO.concat(FileFiltersDTO).validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    return res.json(convertFilesToDTO(await FileRepository.filterFiles(
      validatedParams.entity,
      validatedParams.limit,
      validatedParams.offset,
      validatedParams.sortBy,
      validatedParams.sortOrder,
    ))).end();
  } catch (err) {
    return next(err);
  }
};

export const downloadFile = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UidDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { path: filePath, filename } = await FileService.getFile(validatedParams.uid);
    return res.download(filePath, filename);
  } catch (err) {
    return next(err);
  }
};

export const deleteFile = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UidDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await FileService.removeFile(validatedParams.uid);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};
