import { checkDatabaseConnection } from '../services/healthService.js';

export const healthCheck = async (req, res, next) => {
  try {
    res.status(200).json({
      status: 'Ok',
      uptime: process.uptime(),
    });
  } catch (err) {
    next(err);
  }
};

export const readinessCheck = async (req, res, next) => {
  try {
    const dbConnected = await checkDatabaseConnection();
    res.sendStatus(dbConnected ? 200 : 503);
  } catch (err) {
    next(err);
  }
};
