import { log } from '../logging/logger.js';
import * as TranslationRepository from '../database/repositories/translationRepository.js';
import * as LanguageRepository from '../database/repositories/languageRepository.js';
import TranslationListDTO from '../inputDTOs/translationListDTO.js';
import * as TranslationService from '../services/translationService.js';
import { convertTranslationsToDTO } from '../converters/translationConverter.js';
import LanguageRequiredDTO from '../inputDTOs/languageRequiredDTO.js';
import { ETranslation } from '../constants/ETranslation.js';
import { convertLanguagesToDTO } from '../converters/languageConverter.js';

export const getTranslationsByLanguage = async (req, res, next) => {
  try {
    const { error, value: language } = LanguageRequiredDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const translations = await TranslationRepository.getTranslationsByLanguageCode(language.languageCode);

    return res.status(200).json(convertTranslationsToDTO(translations));
  } catch (err) {
    return next(err);
  }
};

export const getTranslationsCsvByLanguage = async (req, res, next) => {
  try {
    const { error, value: language } = LanguageRequiredDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const file = await TranslationService.getTranslationCsv(language.languageCode);
    res.set('Content-Type', 'text/csv');
    res.set('Content-Disposition', `attachment;filename=${language.languageCode}.csv`);
    return res.send(file);
  } catch (err) {
    return next(err);
  }
};

export const updateTranslationsWithCsv = async (req, res, next) => {
  try {
    const { error, value: language } = LanguageRequiredDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await TranslationService.updateTranslationsCsv(language.languageCode, req.files);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const getTemplateCsv = async (req, res, next) => {
  try {
    const file = await TranslationService.createTemplateCsv();
    res.set('Content-Type', 'text/csv');
    res.set('Content-Disposition', 'attachment;filename=template.csv');
    return res.send(file);
  } catch (err) {
    return next(err);
  }
};

export const addTranslations = async (req, res, next) => {
  try {
    const { error, value: translationList } = TranslationListDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await TranslationService.addTranslations(translationList.translations);

    return res.status(200).json();
  } catch (err) {
    return next(err);
  }
};

export const getAllLanguages = async (req, res, next) => {
  try {
    const languages = await LanguageRepository.getActiveLanguages();
    res.status(200).json(convertLanguagesToDTO(languages));
  } catch (err) {
    next(err);
  }
};
