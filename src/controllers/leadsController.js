import { log } from '../logging/logger.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import { convertLeadToDTO, convertLeadsToDTO } from '../converters/leadConverter.js';
import * as UserService from '../services/userService.js';
import PaginationDTO from '../inputDTOs/paginationDTO.js';
import LeadFiltersDTO from '../inputDTOs/leadFiltersDTO.js';
import AddLeadDTO from '../inputDTOs/addUserDTO.js';
import { ETranslation } from '../constants/ETranslation.js';
import { convertLeadsToDTO } from '../converters/leadConverter.js';

const getLeads = async (req, res, next) => {
  try {
    // Combine PaginationDTO with LeadFiltersDTO
    const { error, value: validatedParams } = PaginationDTO.concat(LeadFiltersDTO).validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const leadsResult = await UserRepository.getAllFiltered(
      validatedParams.limit,
      validatedParams.offset,
      validatedParams.sortBy,
      validatedParams.sortOrder,
      validatedParams.email,
      validatedParams.name,
      validatedParams.nif,
      validatedParams.location,
      validatedParams.district,
      validatedParams.county,
      validatedParams.status,
    );

    return res.status(200).json(convertLeadsToDTO(leadsResult));
  } catch (err) {
    return next(err);
  }
};

const addLead = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = AddLeadDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const lead = await UserService.addUser(
      validatedParams.status,
      validatedParams.name,
      validatedParams.email,
      validatedParams.contact,
      validatedParams.postal_code,
      validatedParams.location,
      validatedParams.district,
      validatedParams.county,
      validatedParams.nif,
      validatedParams.origin,
      validatedParams.message,
      validatedParams.userId,
      validatedParams.assignedDate,
      validatedParams.closingDate,
      validatedParams.closingReasonId,
      validatedParams.closingReason,
      validatedParams.creationDate,
      validatedParams.integration_gesmat_course,
      validatedParams.integration_gesmat_assinged_polo,
      validatedParams.integration_gesmat_auth_rgpd_4,
      validatedParams.integration_gesmat_auth_rgpd_5,
      validatedParams.integration_gesmat_sent_gocontact,
      validatedParams.integration_gesmat_assigned_gocontact,
    );

    return res.status(200).json(convertLeadToDTO(lead));
  } catch (err) {
    return next(err);
  }
};

export default {
  addLead,
  getLeads
}