import { log } from '../logging/logger.js';
import * as WorkplaceRepository from '../database/repositories/workplaceRepository.js';
import * as WorkplaceService from '../services/workplaceService.js';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import { ETranslation } from '../constants/ETranslation.js';
import UpdateUserWorkplaceDTO from '../inputDTOs/updateWorkplaceData.js';
import { convertWorkplaceToDTO } from '../converters/workplaceConverter.js';

export const getWorkplaceData = async (req, res, next) => {
  try {
    const authUserId = getStore().get(EAls.USER_ID);
    return res.send(convertWorkplaceToDTO(await WorkplaceRepository.getUserWorkplace(authUserId)));
  } catch (err) {
    return next(err);
  }
};

export const setWorkplaceData = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UpdateUserWorkplaceDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    const authUserId = getStore().get(EAls.USER_ID);
    await WorkplaceService.updateWorkplace(authUserId, validatedParams);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const getOpentabsCount = async (req, res, next) => {
  try {
    const authUserId = getStore().get(EAls.USER_ID);
    return res.json(await WorkplaceRepository.countUserOpenTabs(authUserId));
  } catch (err) {
    return next(err);
  }
};

export const deleteWorkplace = async (req, res, next) => {
  try {
    const authUserId = getStore().get(EAls.USER_ID);
    await WorkplaceRepository.deleteUserWorkplace(authUserId);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};
