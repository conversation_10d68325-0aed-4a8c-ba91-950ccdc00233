import { log } from '../logging/logger.js';
import { convertUsersToDTO, convertUserToDTO } from '../converters/userConverter.js';
import { EUserState } from '../constants/EUserState.js';
import { convertUserStatesToDTO } from '../converters/userStateConverter.js';
import * as UserService from '../services/userService.js';
import * as EmailService from '../services/emailService.js';
import PaginationDTO from '../inputDTOs/paginationDTO.js';
import UserFiltersDTO from '../inputDTOs/userFiltersDTO.js';
import AddUserDTO from '../inputDTOs/addUserDTO.js';
import GetUserDTO from '../inputDTOs/getUserDTO.js';
import LanguageDTO from '../inputDTOs/languageDTO.js';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import { ETranslation } from '../constants/ETranslation.js';
import OtpDTO from '../inputDTOs/otpDTO.js';
import UserIdDTO from '../inputDTOs/userIdDTO.js';
import UpdateUserDTO from '../inputDTOs/updateUserDTO.js';
import UpdateOwnUserDTO from '../inputDTOs/updateOwnUserDTO.js';
import * as SubRoleRepository from '../database/repositories/subRoleRepository.js';
import { convertSubRolesToDTO } from '../converters/subRoleConverter.js';

export const getUsers = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = PaginationDTO.concat(UserFiltersDTO).validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    return res.status(200).json(convertUsersToDTO(await UserService.getAllUsersFiltered(validatedParams)));
  } catch (err) {
    return next(err);
  }
};

export const getUserStates = async (req, res, next) => {
  try {
    res.status(200).json(convertUserStatesToDTO(Object.values(EUserState)));
  } catch (err) {
    next(err);
  }
};

export const getUserById = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = GetUserDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertUserToDTO(await UserService.getUser(validatedParams.user_id)));
  } catch (err) {
    return next(err);
  }
};

export const getAuthUser = async (req, res, next) => {
  try {
    return res.status(200).json(convertUserToDTO(await UserService.getAuthUser()));
  } catch (err) {
    return next(err);
  }
};

export const updateOtherUser = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UpdateUserDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: userIdDto } = UserIdDTO.validate(req.params);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.updateUser(
      validatedParams,
      userIdDto,
    );

    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const updateOwnUser = async (req, res, next) => {
  try {
    const authUserId = getStore().get(EAls.USER_ID);
    const { error, value: validatedParams } = UpdateOwnUserDTO.validate({ user_id: authUserId, ...req.body });
    if (error) {
      return res.status(400).json({ error: error.details.map((err) => err.message) });
    }
    await UserService.updateOwnUserProfile(validatedParams);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const resendUpdateEmailOtp = async (req, res, next) => {
  try {
    const { error, value: language } = LanguageDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await EmailService.resendUpdateEmailOtpEmail(getStore().get(EAls.USER_ID), language.languageCode);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const addUser = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = AddUserDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    return res.status(200).json(convertUserToDTO(await UserService.addUser(
      validatedParams.email,
      validatedParams.subRoleCode,
      validatedParams.roleCode,
      validatedParams.phone,
      validatedParams.name,
      language.languageCode,
    )));
  } catch (err) {
    return next(err);
  }
};

export const validateEmailOtp = async (req, res, next) => {
  try {
    const { error, value: otpDto } = OtpDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await UserService.updateEmailWithOtp(otpDto.otp);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const logout = async (req, res, next) => {
  try {
    await UserService.logout();

    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const resetPassword = async (req, res, next) => {
  try {
    const { error, value: userIdDTO } = UserIdDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.sendRecoverPasswordOtpOtherUser(req, res, userIdDTO.user_id, language.languageCode);

    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const getUserSubroles = async (req, res, next) => {
  try {
    res.status(200).json(convertSubRolesToDTO(await SubRoleRepository.getAllSubroles()));
  } catch (err) {
    next(err);
  }
};
