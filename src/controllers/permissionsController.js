import { log } from '../logging/logger.js';
import * as SubRoleFunctionalityRepository from '../database/repositories/subRoleFunctionalityRepository.js';
import { groupPermissionsBySubRoleAndModule, formatSubRoleFunctionalities } from '../converters/permissionsConverter.js';
import UidDTO from '../inputDTOs/UidDTO.js';
import AddSubRoleDTO from '../inputDTOs/addSubRoleDTO.js';
import DuplicateSubRoleDTO from '../inputDTOs/duplicateSubRoleDTO.js';
import { ETranslation } from '../constants/ETranslation.js';
import * as PermissionService from '../services/permissionsService.js';
import UpdateSubRolePermissionsDTO from '../inputDTOs/updateSubRolePermissionsDTO.js';

export const getModulesBySubRole = async (req, res, next) => {
  try {
    return res.status(200).json(groupPermissionsBySubRoleAndModule((await SubRoleFunctionalityRepository.findAll())));
  } catch (err) {
    return next(err);
  }
};

export const getSubRolePermissions = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UidDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(formatSubRoleFunctionalities(await PermissionService.getSubrolePermissions(validatedParams.uid)));
  } catch (err) {
    return next(err);
  }
};

export const updateSubRolePermissions = async (req, res, next) => {
  try {
    const { error, value: subRoleId } = UidDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    const { error2, value: validatedParams } = UpdateSubRolePermissionsDTO.validate(req.body);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await PermissionService.updatePermissions(subRoleId.uid, validatedParams);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const createSubRole = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = AddSubRoleDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await PermissionService.createSubRoleWithPermissions(validatedParams);
    return res.status(201).end();
  } catch (err) {
    return next(err);
  }
};

export const duplicateSubRole = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = DuplicateSubRoleDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await PermissionService.duplicateSubRole(validatedParams);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};

export const deleteSubRole = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UidDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await PermissionService.deleteSubRole(validatedParams.uid);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};
