import { log } from '../logging/logger.js';
import LoginDTO from '../inputDTOs/loginDTO.js';
import RegisterDTO from '../inputDTOs/registerDTO.js';
import * as AuthService from '../services/authService.js';
import * as UserService from '../services/userService.js';
import OtpDTO from '../inputDTOs/otpDTO.js';
import EmailDTO from '../inputDTOs/emailDTO.js';
import RecoverPasswordOtpDTO from '../inputDTOs/recoverPasswordOtpDTO.js';
import LanguageDTO from '../inputDTOs/languageDTO.js';
import ValidateAccountDTO from '../inputDTOs/validateAccountDTO.js';
import { ETranslation } from '../constants/ETranslation.js';

export const login = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = LoginDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const tokens = await AuthService.authenticateWithRandomResponseTime(
      req,
      res,
      validatedParams.email,
      validatedParams.password,
      language.languageCode,
    );

    return res.status(200).json(tokens);
  } catch (err) {
    return next(err);
  }
};

export const resendLoginOtp = async (req, res, next) => {
  try {
    const { error, value: language } = LanguageDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const token = await AuthService.resendLoginOtp(language.languageCode);

    return res.status(200).json(token);
  } catch (err) {
    return next(err);
  }
};

export const register = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = RegisterDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.addExternalUser(
      validatedParams.email,
      validatedParams.phone,
      validatedParams.name,
      language.languageCode,
      validatedParams.password,
      validatedParams.address,
      validatedParams.nif,
    );

    return res.status(201).end();
  } catch (err) {
    return next(err);
  }
};

export const validateOtp = async (req, res, next) => {
  try {
    const { error, value: otpDto } = OtpDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const tokens = await AuthService.validateOtp(otpDto.otp);

    return res.status(200).json(tokens);
  } catch (err) {
    return next(err);
  }
};

export const refreshTokens = async (req, res, next) => {
  try {
    const tokens = await AuthService.refreshTokens();

    res.status(200).json(tokens);
  } catch (err) {
    next(err);
  }
};

export const getRecoverPasswordEmail = async (req, res, next) => {
  try {
    const { error, value: emailDto } = EmailDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.sendRecoverPasswordOtp(req, res, emailDto.email, language.languageCode);

    return res.status(200).json();
  } catch (err) {
    return next(err);
  }
};

export const recoverPassword = async (req, res, next) => {
  try {
    const { error, value: recoverPasswordOtpDto } = RecoverPasswordOtpDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.recoverPasswordWithOtpOrToken(
      req,
      res,
      recoverPasswordOtpDto.email,
      recoverPasswordOtpDto.newPassword,
      recoverPasswordOtpDto.otpOrToken,
    );

    return res.status(200).json();
  } catch (err) {
    return next(err);
  }
};

export const validateAccount = async (req, res, next) => {
  try {
    const { error, value: validateAccountDto } = ValidateAccountDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await UserService.validateAccount(
      req,
      res,
      validateAccountDto.email,
      validateAccountDto.password,
      validateAccountDto.otp,
      validateAccountDto.phone,
    );

    return res.status(200).json();
  } catch (err) {
    return next(err);
  }
};

export const resendValidateAccountOtp = async (req, res, next) => {
  try {
    const { error, value: emailDto } = EmailDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    const { error2, value: language } = LanguageDTO.validate(req.query);
    if (error2) {
      log('error', error2.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }

    await AuthService.resendValidateAccountOtp(req, res, emailDto.email, language.languageCode);

    return res.status(200).json();
  } catch (err) {
    return next(err);
  }
};
