import { log } from '../logging/logger.js';
import addTeamDTO from '../inputDTOs/addTeamDTO.js';
import addBranchDTO from '../inputDTOs/addBranchDTO.js';
import { ETranslation } from '../constants/ETranslation.js';
import * as TeamService from '../services/teamService.js';
import { convertTeamToDTO, convertTeamsToDTO } from '../converters/teamConverter.js';
import { convertUsersToDTO } from '../converters/userConverter.js';
import TeamFiltersDTO from '../inputDTOs/teamFiltersDTO.js';
import BranchFiltersDTO from '../inputDTOs/branchFiltersDTO.js';
import UpdateTeamDTO from '../inputDTOs/updateTeamDTO.js';
import TeamIdDTO from '../inputDTOs/teamIdDTO.js';
import UpdateBranchDTO from '../inputDTOs/updateBranchDTO.js';
import BranchIdDTO from '../inputDTOs/branchIdDTO.js';
import { convertBranchesToDTO, convertBranchToDTO } from '../converters/branchConverter.js';
import AssociateUsersToTeamDTO from '../inputDTOs/associateUsersToTeamDTO.js';
import UserIdDTO from '../inputDTOs/userIdDTO.js';
import RemoveUserFromTeamDTO from '../inputDTOs/removeUserFromTeamDTO.js';
import * as TeamRepository from '../database/repositories/teamRepository.js';

export const addBranch = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = addBranchDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(201).json(convertBranchToDTO(await TeamService.addBranch(validatedParams)));
  } catch (err) {
    return next(err);
  }
};

export const addTeam = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = addTeamDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(201).json(convertTeamToDTO(await TeamService.addTeam(validatedParams)));
  } catch (err) {
    return next(err);
  }
};

export const getBranches = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = BranchFiltersDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertBranchesToDTO(await TeamRepository.getAllBranchesFiltered(validatedParams)));
  } catch (err) {
    return next(err);
  }
};

export const getTeams = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = TeamFiltersDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertTeamsToDTO(await TeamService.getAllTeamsFiltered(validatedParams)));
  } catch (err) {
    return next(err);
  }
};

export const updateBranch = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UpdateBranchDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    const { error2, value: branchIdDto } = BranchIdDTO.validate(req.body);
    if (error2) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertBranchToDTO(await TeamService.updateBranch(validatedParams, branchIdDto)));
  } catch (err) {
    return next(err);
  }
};

export const updateTeam = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UpdateTeamDTO.validate(req.query);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    const { error2, value: teamIdDto } = TeamIdDTO.validate(req.body);
    if (error2) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertTeamToDTO(await TeamService.updateTeam(validatedParams, teamIdDto)));
  } catch (err) {
    return next(err);
  }
};

export const associateUsersToTeam = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = AssociateUsersToTeamDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(await TeamService.associateUsersToTeam(validatedParams));
  } catch (err) {
    return next(err);
  }
};

export const getTeamsIdsByUserID = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = UserIdDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(await TeamService.getTeamsIdsByUserID(validatedParams));
  } catch (err) {
    return next(err);
  }
};

export const areUsersInSameTeam = async (req, res, next) => {
  try {
    const { userId } = req.body;
    const { otherUserId } = req.body;
    const areInTheSameTeam = await TeamService.areUsersInSameTeam(userId, otherUserId);
    return res.status(200).json(areInTheSameTeam);
  } catch (err) {
    return next(err);
  }
};

export const getUsersByTeamId = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = TeamIdDTO.validate(req.params);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    return res.status(200).json(convertUsersToDTO(await TeamService.getUsersByTeamId(validatedParams)));
  } catch (err) {
    return next(err);
  }
};
export const removeUserFromTeam = async (req, res, next) => {
  try {
    const { error, value: validatedParams } = RemoveUserFromTeamDTO.validate(req.body);
    if (error) {
      log('error', error.details[0].message);
      return res.status(400).json({ error: ETranslation.REQUEST_BODY_INVALID });
    }
    await TeamService.removeUserFromTeam(validatedParams);
    return res.status(200).end();
  } catch (err) {
    return next(err);
  }
};
