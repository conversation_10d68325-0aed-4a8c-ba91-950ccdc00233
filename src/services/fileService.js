import fs from 'fs/promises';
import { constants } from 'fs';
import crypto from 'crypto';
import path from 'path';
import { log } from '../logging/logger.js';
import { ETranslation } from '../constants/ETranslation.js';
import RequestException from '../exceptions/requestException.js';
import * as FileRepository from '../database/repositories/fileRepository.js';

const FILE_DIRECTORY = process.env.FILE_DIRECTORY || './files';

export const uploadFile = async (files, associatedEntity) => {
  if (files.length === 0) {
    log('error', 'No CSV file uploaded.');
    throw new RequestException(ETranslation.MISSING_FILE);
  }
  if (files.length > 1) {
    log('error', 'Cannot upload more than 1 file.');
    throw new RequestException(ETranslation.MULTIPLE_FILES_UPLOADED);
  }
  const file = files[0];
  const buffer = await fs.readFile(file.path);
  const hash = crypto.createHash('sha256').update(buffer).digest('hex');
  const existingFile = await FileRepository.findFileByHash(hash);

  let storagePath;
  if (existingFile) {
    storagePath = existingFile.storage_path;
  } else {
    // Save to disk (use hash as filename to ensure uniqueness)
    storagePath = `${hash}${path.extname(file.filename)}`;
    // Ensure the uploads directory exists
    const uploadsDir = path.dirname(path.join(FILE_DIRECTORY, storagePath));
    try {
      await fs.access(uploadsDir, constants.F_OK);
    } catch {
      await fs.mkdir(uploadsDir, { recursive: true });
    }

    // Write the file
    await fs.writeFile(path.join(FILE_DIRECTORY, storagePath), buffer);
  }

  await FileRepository.createFile(file.filename, file.mimetype, file.size, storagePath, hash, associatedEntity);
};

export const getFile = async (fileId) => {
  const file = await FileRepository.findFileById(fileId);
  if (!file) {
    log('error', 'File not found.');
    throw new RequestException(ETranslation.FILE_NOT_FOUND);
  }
  return {
    path: path.join(FILE_DIRECTORY, file.storage_path),
    filename: file.filename,
  };
};

export const removeFile = async (fileId) => {
  const file = await FileRepository.findFileById(fileId);
  if (!file) {
    log('error', 'File not found.');
    throw new RequestException(ETranslation.FILE_NOT_FOUND);
  }
  await file.destroy();

  // If there is no file with the same hash delete file from file system
  if (!(await FileRepository.findFileByHash(file.content_hash))) {
    try {
      await fs.unlink(path.join(FILE_DIRECTORY, file.storage_path));
      log('info', `Deleted file from disk: ${file.storage_path}`);
    } catch (fsError) {
      log('warn', `Could not delete file from disk: ${fsError.message}`);
    }
  }
};
