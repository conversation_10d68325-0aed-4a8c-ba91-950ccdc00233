import * as UserRepository from '../database/repositories/userRepository.js';
import {
  hashPassword, validatePasswordHash, isValidPassword, generateRandomHashedPassword,
} from '../utils/passwordUtil.js';
import { log } from '../logging/logger.js';
import { manageSubRolesOfUser, addSubRoleOfUser } from './roleService.js';
import { areUsersInSameTeam, getTeamsIdsByUserID } from './teamService.js';
import { findRolesByUserId } from '../database/repositories/roleRepository.js';
import * as EmailService from './emailService.js';
import { EOtp } from '../constants/EOtp.js';
import { validateOtpOrTokenByType } from './otpService.js';
import * as DeviceCookieService from './deviceCookieService.js';
import { EUserState } from '../constants/EUserState.js';
import * as LanguageRepository from '../database/repositories/languageRepository.js';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import { DEFAULT_LANGUAGE } from '../constants/ELanguage.js';
import { ETranslation } from '../constants/ETranslation.js';
import { ESubRole } from '../constants/ESubRole.js';
import { ERole } from '../constants/ERole.js';
import * as OtpRepository from '../database/repositories/otpRepository.js';
import { findSubRolesByUserId } from '../database/repositories/subRoleRepository.js';
import { findFunctionalitiesPermissionsByUserId } from '../database/repositories/functionalityRepository.js';
import RequestException from '../exceptions/requestException.js';
import { EEnvironmentVariableValues } from '../constants/EEnvironmentVariableValues.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import { EPermissionValue } from '../constants/EPermissionValue.js';
import { getPermissionValue } from './permissionsService.js';

export const getAllPermissionsByUserId = async (userId) => {
  const roles = await findRolesByUserId(userId);
  const subRoles = await findSubRolesByUserId(userId);
  const functionalities = await findFunctionalitiesPermissionsByUserId(userId);

  const rolesCodes = roles.map((role) => role.code);
  const subRolesCodes = subRoles.map((subRole) => subRole.code);
  const functionalitiesCodes = functionalities.map((func) => ({
    code: func.functionality_code,
    permissions: func.permissions,
  }));

  return [...rolesCodes, ...subRolesCodes, ...functionalitiesCodes];
};

export const getAuthUser = async () => {
  const userId = getStore().get(EAls.USER_ID);
  return UserRepository.findById(userId);
};

export const addExternalUser = async (email, phone, name, languageCode, passwordPlainText, address, nif) => {
  let user = await UserRepository.findByEmail(email);

  if (user) {
    log('warn', 'User already exists.');
    throw new RequestException(ETranslation.USER_ALREADY_EXISTS);
  }

  if (!isValidPassword(passwordPlainText)) {
    log('warn', 'Invalid password format provided by user.');
    throw new RequestException(ETranslation.INVALID_NEW_PASSWORD);
  }

  const passwordHash = await hashPassword(passwordPlainText);

  log('info', `Creating new user with email ${email}`);
  if (process.env.ACCOUNT_VALIDATION === EEnvironmentVariableValues.TRUE) {
    user = await UserRepository.createUser(email, name, phone, passwordHash, address, nif, languageCode, EUserState.PENDING_VALIDATION);
    await addSubRoleOfUser(user.id, ESubRole.USER, ERole.EXTERNAL_USER);
    log('info', `Sending account validation OTP email to user ${user.id}`);
    await EmailService.sendAccountValidationEmail(user.id, languageCode, process.env.VALIDATION_TYPE);
  } else {
    user = await UserRepository.createUser(email, name, phone, passwordHash, address, nif, languageCode, EUserState.ACTIVE);
    await addSubRoleOfUser(user.id, ESubRole.USER, ERole.EXTERNAL_USER);
  }

  return user;
};

export const addUser = async (email, subRoleCode, roleCode, phone, name, address, nif, languageCode = null) => {
  const permissionValue = await getPermissionValue(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.CREATE);
  const isAll = permissionValue.value === EPermissionValue.ALL;

  if (!isAll) {
    throw new RequestException(ETranslation.PERMISSIONS_DENIED);
  }
  let user = await UserRepository.findByEmail(email);

  if (user) {
    log('warn', 'User already exists.');
    throw new RequestException(ETranslation.USER_ALREADY_EXISTS);
  }

  user = await UserRepository.createUser(email, name, phone, (await generateRandomHashedPassword()), address, nif, languageCode);
  try {
    const subRoleUserRelation = await addSubRoleOfUser(user.id, subRoleCode, roleCode);
    log('debug', 'SubRole added with success:', subRoleUserRelation);
  } catch (err) {
    log('error', `Error in addSubRoleOfUser: ${err.message}`);
    throw err;
  }
  await EmailService.sendAccountValidationEmail(user.id, languageCode, process.env.VALIDATION_TYPE);

  return user;
};

export const sendRecoverPasswordOtp = async (req, res, email, languageCode) => {
  let isToSendOtp = true;
  let user = null;
  try {
    if (await DeviceCookieService.isUserLocked(email)
            || (await DeviceCookieService.getCookieFromRequest(req) != null
            && (
              !(await DeviceCookieService.isValidDeviceCookie(req))
                || await DeviceCookieService.isDeviceLocked(req)
            ))
    ) {
      log('warn', 'Locked by device cookie verification.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }

    user = await UserRepository.findByEmail(email);
    if (!user) {
      log('warn', 'User not found.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }

    if (user.user_state !== EUserState.ACTIVE) {
      log('warn', 'User is not active.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }
  } catch (err) {
    log('error', err.message);
    isToSendOtp = false;
  } finally {
    await DeviceCookieService.handleLoginFail(req, res, email);
  }

  if (isToSendOtp) {
    await EmailService.sendRecoverPasswordEmail(user.id, languageCode, process.env.VALIDATION_TYPE);
  }

  // always return true in order to prevent user enumeration
  return true;
};

export const validateUserPermissions = async (functionalityNeeded, action, userId) => {
  const permissionValue = await getPermissionValue(functionalityNeeded, action);
  const isAll = permissionValue.value === EPermissionValue.ALL;
  const isOwn = EPermissionValue.value === EPermissionValue.OWN && userId === permissionValue.userId;
  const isTeam = EPermissionValue.value === EPermissionValue.TEAM && (await areUsersInSameTeam(userId, permissionValue.userId)).sameTeam;

  if (!isAll && !isOwn && !isTeam) {
    throw new RequestException(ETranslation.PERMISSIONS_DENIED);
  }
};

const validationUserLock = async (email, req) => {
  if (await DeviceCookieService.isUserLocked(email)
    || (await DeviceCookieService.getCookieFromRequest(req) != null
    && (
      !(await DeviceCookieService.isValidDeviceCookie(req))
        || await DeviceCookieService.isDeviceLocked(req)
    ))
  ) {
    log('warn', 'Locked by device cookie verification.');
    throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
  }
};
const validateUserForPasswordRecovery = async (user) => {
  if (!user) {
    log('warn', 'User not found.');
    throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
  }

  if (
    user.user_state !== EUserState.ACTIVE
    && user.user_state !== EUserState.PENDING_RESTART_PASSWORD
  ) {
    log('warn', 'User is not active.');
    throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
  }
};

export const sendRecoverPasswordOtpOtherUser = async (req, res, userId, languageCode) => {
  let isToSendOtp = true;
  const user = await UserRepository.findById(userId);
  await validateUserPermissions(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDATE, user);
  try {
    await validationUserLock(user.email, req);

    await validateUserForPasswordRecovery(user);
  } catch (err) {
    log('error', err.message);
    isToSendOtp = false;
  } finally {
    await DeviceCookieService.handleLoginFail(req, res, user.email);
  }

  if (isToSendOtp) {
    user.user_state = EUserState.PENDING_RESTART_PASSWORD;
    await UserRepository.updateUser(user);
    await EmailService.sendRecoverPasswordEmail(user.id, languageCode, process.env.VALIDATION_TYPE);
  }

  // always return true in order to prevent user enumeration
  return true;
};

export const recoverPasswordWithOtpOrToken = async (req, res, email, newPassword, otpOrToken) => {
  await validationUserLock(email, req);

  let user = null;
  try {
    if (!isValidPassword(newPassword)) {
      log('info', 'Invalid password.');
      throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
    }

    user = await UserRepository.findByEmail(email);
    await validateUserForPasswordRecovery(user);

    const isValidOtp = await validateOtpOrTokenByType(user.id, EOtp.RECOVER_PASSWORD, otpOrToken);
    if (!isValidOtp) {
      log('info', 'Otp validation failed.');
      throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
    }
  } catch (err) {
    await DeviceCookieService.handleLoginFail(req, res, email);
    throw err;
  }

  await DeviceCookieService.handleLoginSuccessful(req, res, email);

  user.password_hash = await hashPassword(newPassword);
  user.last_password_reset = new Date();
  if (user.user_state === EUserState.PENDING_RESTART_PASSWORD) {
    user.user_state = EUserState.ACTIVE;
  }
  await UserRepository.updateUser(user);

  return true;
};

export const validateAccount = async (req, res, email, password, otp, phone) => {
  if (await DeviceCookieService.isUserLocked(email)
        || (await DeviceCookieService.getCookieFromRequest(req) != null
        && (
          !(await DeviceCookieService.isValidDeviceCookie(req))
            || await DeviceCookieService.isDeviceLocked(req)
        ))
  ) {
    log('warn', 'Locked by device cookie verification.');
    throw new RequestException(ETranslation.ACCOUNT_VALIDATION_FAILED);
  }

  let user = null;
  try {
    if (!isValidPassword(password)) {
      log('info', 'Invalid password.');
      throw new RequestException(ETranslation.ACCOUNT_VALIDATION_FAILED);
    }

    user = await UserRepository.findByEmail(email);
    if (!user) {
      log('info', 'User not found.');
      throw new RequestException(ETranslation.ACCOUNT_VALIDATION_FAILED);
    }

    const isValidOtp = await validateOtpOrTokenByType(user.id, EOtp.PENDING_VALIDATION, otp);
    if (!isValidOtp) {
      log('info', 'Otp validation failed.');
      throw new RequestException(ETranslation.ACCOUNT_VALIDATION_FAILED);
    }
  } catch (err) {
    await DeviceCookieService.handleLoginFail(req, res, email);
    throw err;
  }

  await DeviceCookieService.handleLoginSuccessful(req, res, email);

  // If password was sent update password hash
  if (password) {
    user.password_hash = await hashPassword(password);
  }

  if (phone) {
    user.phone = phone;
  }

  user.user_state = EUserState.ACTIVE;
  user.validation_date = new Date();
  await UserRepository.updateUser(user);

  return true;
};

export const getUser = async (userId) => {
  await validateUserPermissions(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ, userId);
  const user = await UserRepository.findById(userId);
  if (!user) {
    log('info', 'User not found.');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }
  return user;
};

export const validateUserPermissionsToFilter = async (functionalityNeeded, action, userId, params) => {
  const permission = await getPermissionValue(functionalityNeeded, action);
  if (permission.value === EPermissionValue.ALL) {
    params.id = permission.userId;
  }
  if (permission.value === EPermissionValue.TEAM) {
    const teams = await getTeamsIdsByUserID(permission.userId);
    const teamIds = teams.map((team) => team.team_id);
    params.teams = teamIds;
    if (params.teams.length === 0) {
      params.id = permission.userId;
    }
  }
  if (permission.value !== EPermissionValue.OWN
    && permission.value !== EPermissionValue.TEAM
    && permission.value !== EPermissionValue.ALL && areUsersInSameTeam(userId, permission.userId)) {
    log('warn', 'Permission denied');
    throw new RequestException(ETranslation.PERMISSIONS_DENIED);
  }
  return params;
};
export const getAllUsersFiltered = async (params) => {
  const validatedParams = await validateUserPermissionsToFilter(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ, params.id, params);
  return UserRepository.getAllFiltered(validatedParams);
};

export const updateUser = async (params, userIdDto) => {
  await validateUserPermissions(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDATE, params.userId);
  const user = await UserRepository.findById(userIdDto.user_id);
  if (!user) {
    log('info', 'User not found.');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }

  if (params.email && user.email !== params.email) {
    const userNewEmail = await UserRepository.findByEmail(params.email);
    if (userNewEmail) {
      log('warn', 'User already exists.');
      throw new RequestException(ETranslation.USER_ALREADY_EXISTS);
    }

    // When this option is true the email is only changed after validating the email
    if (process.env.EMAIL_VALIDATION === EEnvironmentVariableValues.TRUE) {
      const languageById = await LanguageRepository.getLanguageById(user.selected_language_id);
      await EmailService.sendUpdateEmailOtpEmail(userIdDto.user_id, params.email, languageById.id, process.env.VALIDATION_TYPE);
    } else { // Otherwise update email directly
      user.email = params.email;
    }
  }
  if (params.name) {
    user.name = params.name;
  }
  if (params.nif) {
    user.nif = params.nif;
  }
  if (params.phone) {
    user.phone = params.phone;
  }
  if (params.state) {
    user.state = params.state;
  }
  if (params.newPassword) {
    user.password_hash = await hashPassword(params.newPassword);
    user.last_password_reset = new Date();
  }
  if (params.languageCode) {
    const language = await LanguageRepository.getLanguageByCode(params.languageCode);
    if (!language) {
      log('info', 'Language not found.');
      throw new RequestException(ETranslation.LANGUAGE_NOT_FOUND);
    }
    user.selected_language_id = language.id;
  }
  await UserRepository.updateUser(user);

  const userIdAuth = getStore().get(EAls.USER_ID);
  if (params.subRoleCode && params.roleCode && userIdAuth !== user.id) {
    await manageSubRolesOfUser(user.id, params.subRoleCode, params.roleCode);
  }
};

export const updateOwnUserProfile = async (params) => {
  await validateUserPermissions(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDATE, params.user_id);
  const user = await UserRepository.findById(params.user_id);
  if (!user) {
    log('info', 'User not found.');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }
  if (params.currentPassword && !await validatePasswordHash(params.currentPassword, user.password_hash)) {
    log('info', 'Incorrect current password.');
    throw new RequestException(ETranslation.PASSWORD_INCORRECT);
  }

  await updateUser(params, params);
};

export const getLanguageCodeOfUser = async (userId) => {
  // If user is not logged in return default language
  if (!userId) {
    return DEFAULT_LANGUAGE;
  }
  const user = await UserRepository.findById(userId);
  const language = await user.getSelectedLanguage();
  return language.code;
};

export const getLanguageSelectedOfAuthUser = async () => {
  const userId = getStore().get(EAls.USER_ID);
  return getLanguageCodeOfUser(userId);
};

export const logout = async () => {
  const user = await getAuthUser();
  user.access_token_version += 1n;
  user.refresh_token_version += 1n;
  await UserRepository.updateUser(user);
};

export const updateEmailWithOtp = async (otp) => {
  const userId = getStore().get(EAls.USER_ID);
  const otpEntry = await OtpRepository.findByUserIdAndTypeAndOtp(userId, EOtp.UPDATE_EMAIL, otp);

  if (!otpEntry) {
    log('info', 'Otp validation failed.');
    throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
  }

  const user = await UserRepository.findById(userId);
  if (!user) {
    log('info', 'User not found.');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }

  if (!await validateOtpOrTokenByType(userId, EOtp.UPDATE_EMAIL, otpEntry.otp)) {
    log('info', 'Otp validation failed.');
    throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
  }

  user.email = otpEntry.newField;
  await UserRepository.updateUser(user);
};

export const isUserMerchant = async (userId) => (await findRolesByUserId(userId)).some((x) => x.code === ERole.MERCHANT);
