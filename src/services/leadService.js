
import RequestException from '../exceptions/requestException.js';
import { ETranslation } from '../constants/ETranslation.js';
import leadRepository from '../database/repositories/leadRepository.js';
import { log } from 'winston';

const containsSpecialCharacters = async (str) => {
  const regex = /[^a-zA-Z0-9\s]/;
  return regex.test(str);
}

const isValidEmail = async (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

// TODO: needs validations
const addLead = async (lead) => {
  const { contact, location, district, county, nif } = lead;
  const lead = await leadRepository.findByNif(nif);
  if (lead) {
    log('warn', 'A lead already exists with this NIF.');
    throw new RequestException(ETranslation.LEAD_ALREADY_EXISTS);
  }

  if(contact.length !== 9) {
    log('warn', 'Invalid phone number.'); 
    throw new RequestException(ETranslation.LEAD_BAD_CONTACT);
  }
  if([location, district, county].map(async (property) => {
    return await containsSpecialCharacters(property);
  }).includes(false)) {
    log('warn', 'Invalid location/district/county due to special character.');
    throw new RequestException(ETranslation.LEAD_SPECIAL_CHARACTER);
  }
  if(isValidEmail(email)) {
    log('warn', 'Invalid email.');    
    throw new RequestException(ETranslation.LEAD_BAD_EMAIL);
  }
  
  return await leadRepository.createLead(lead);
};

// TODO: needs validations
const updateLead = async (lead) => {
  const { id } = lead;
  const lead = await leadRepository.findById(id);
  if (!lead) {
    log('info', 'Lead not found.');
    throw new RequestException(ETranslation.LEAD_NOT_FOUND);
  }

  return await leadRepository.updateLead(lead);
};

// TODO: needs validations
const deleteLead = async (id) => {
  const lead = await leadRepository.findById(id);
  if (!lead) {
    log('info', 'Lead not found.');
    throw new RequestException(ETranslation.LEAD_NOT_FOUND);
  }

  return await leadRepository.deleteLeadById(id);
}

// TODO: needs validations
const getLead = async (id) => {
  const lead = await leadRepository.findById(id);
  if (!lead) {
    log('info', 'Lead not found.');
    throw new RequestException(ETranslation.LEAD_NOT_FOUND);
  }
  return lead;
}

export default {
  addLead,
  updateLead,
  deleteLead,
  getLead,
}