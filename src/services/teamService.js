import { log } from '../logging/logger.js';
import * as TeamRepository from '../database/repositories/teamRepository.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import * as UserTeamRepository from '../database/repositories/userTeamRepository.js';
import { ETranslation } from '../constants/ETranslation.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import RequestException from '../exceptions/requestException.js';
import { EPermissionValue } from '../constants/EPermissionValue.js';
import { getPermissionValue } from './permissionsService.js';

const validateUserPermissionsAll = async (functionalityNeeded, action) => {
  const permissionValue = await getPermissionValue(functionalityNeeded, action);
  const isAll = permissionValue.value === EPermissionValue.ALL;
  if (!isAll) {
    throw new RequestException(ETranslation.PERMISSIONS_DENIED);
  }
};

export const addBranch = async (params) => {
  const branch = await TeamRepository.findBranchByName(params.name);
  if (branch) {
    log('warn', 'Branch already exists');
    throw new RequestException(ETranslation.BRANCH_ALREADY_EXISTS);
  }
  return TeamRepository.createBranch(params.name);
};

export const addTeam = async (params) => {
  const team = await TeamRepository.findTeamByName(params.name);
  if (team) {
    log('warn', 'Team already exists');
    throw new RequestException(ETranslation.TEAM_ALREADY_EXISTS);
  }

  const branch = await TeamRepository.findBranchByName(params.branch);
  if (!branch) {
    log('warn', 'Branch does not exist');
    throw new RequestException(ETranslation.BRANCH_DOES_NOT_EXIST);
  }
  const newTeam = await TeamRepository.createTeam(params.name, branch.id);
  return TeamRepository.findTeamByName(newTeam.name);
};

export const getAllTeamsFiltered = async (params) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ);

  if (params.branch) {
    const branch = await TeamRepository.findBranchByName(params.branch);
    if (!branch) {
      throw new RequestException(ETranslation.BRANCH_DOES_NOT_EXIST);
    }
    params.branch_id = branch.id;
  }
  return TeamRepository.getAllTeamsFiltered(params);
};

export const updateBranch = async (params, branchIdDto) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDATE);
  const branch = await TeamRepository.findBranchById(branchIdDto.branch_id);
  if (!branch) {
    log('warn', 'Branch does not exist');
    throw new RequestException(ETranslation.BRANCH_DOES_NOT_EXIST);
  }
  if (params.name) {
    branch.name = params.name;
  }
  return TeamRepository.updateBranch(branch);
};

export const updateTeam = async (params, teamIdDto) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDTE);

  const team = await TeamRepository.findTeamById(teamIdDto.team_id);
  if (!team) {
    log('warn', 'Team does not exists');
    throw new RequestException(ETranslation.TEAM_DOES_NOT_EXIST);
  }
  if (params.name) {
    team.name = params.name;
  }
  if (params.branch) {
    const branch = await TeamRepository.findBranchByName(params.branch);
    if (!branch) {
      log('warn', 'Branch does not exists');
      throw new RequestException(ETranslation.BRANCH_DOES_NOT_EXIST);
    }
    team.branch_id = branch.id;
  }
  return TeamRepository.updateTeam(team);
};

export const associateUsersToTeam = async (params) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.UPDATE);

  const team = await TeamRepository.findTeamById(params.team_id);
  if (!team) {
    log('warn', 'Team does not exists');
    throw new RequestException(ETranslation.TEAM_DOES_NOT_EXIST);
  }
  await Promise.all(
    params.user_ids.map(async (userID) => {
      const user = await UserRepository.findById(userID);
      if (user) {
        await UserTeamRepository.associateUserToTeam(team.id, user.id);
      }
    }),
  );
};

export const getTeamsIdsByUserID = async (userId) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ);

  const user = await UserRepository.findById(userId);
  if (!user) {
    log('warn', 'User does not exist');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }
  return UserTeamRepository.findTeamIdsfromUserId(userId);
};

export const areUsersInSameTeam = async (userId, otherUserId) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ);

  if (userId === otherUserId) {
    log('warn', 'Users can not be the same');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }

  const user = await UserRepository.findById(userId);
  const otherUser = await UserRepository.findById(otherUserId);

  if (!user || !otherUser) {
    log('warn', 'User does not exist');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }

  const userTeams = await UserTeamRepository.findTeamIdsfromUserId(user.id);
  const otherUserTeams = await UserTeamRepository.findTeamIdsfromUserId(otherUser.id);

  const userTeamsIds = userTeams.map((item) => item.team_id);
  const otherUserTeamsIds = otherUserTeams.map((item) => item.team_id);

  const commonTeamIds = userTeamsIds.filter((teamId) => otherUserTeamsIds.includes(teamId));

  return {
    sameTeam: commonTeamIds !== null,
    commonTeamIds,
  };
};

export const getUsersByTeamId = async (params) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.READ);

  const team = await TeamRepository.findTeamById(params.team_id);
  if (!team) {
    log('warn', 'Team does not exist');
    throw RequestException(ETranslation.TEAM_DOES_NOT_EXIST);
  }
  return UserTeamRepository.findUsersByTeamId(params.team_id);
};

export const removeUserFromTeam = async (params) => {
  await validateUserPermissionsAll(EFunctionality.USER_MANAGEMENT.code, EPermissionAction.DELETE);

  const team = await TeamRepository.findTeamById(params.team_id);
  if (!team) {
    log('info', 'Team does not exist');
    throw new RequestException(ETranslation.TEAM_DOES_NOT_EXIST);
  }
  const user = await UserRepository.findById(params.user_id);
  if (!user) {
    log('info', 'User does not exist');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }

  return UserTeamRepository.deleteUserFromTeam(params);
};
