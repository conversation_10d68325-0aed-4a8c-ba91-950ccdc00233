import { generateLoginRandomTime } from '../utils/randomGeneratorUtil.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import RequestException from '../exceptions/requestException.js';
import { validatePasswordHash } from '../utils/passwordUtil.js';
import { log } from '../logging/logger.js';
import { createAuthToken } from '../utils/jwtTokenUtil.js';
import * as EmailService from './emailService.js';
import { validateOtpOrTokenByType } from './otpService.js';
import { EOtp } from '../constants/EOtp.js';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import * as DeviceCookieService from './deviceCookieService.js';
import { EUserState } from '../constants/EUserState.js';
import { ETranslation } from '../constants/ETranslation.js';
import { EEnvironmentVariableValues } from '../constants/EEnvironmentVariableValues.js';

const enforceRandomDelay = async (startTime) => {
  const elapsedTime = Date.now() - startTime;
  const remainingDelay = generateLoginRandomTime();

  const delay = remainingDelay - elapsedTime;

  if (delay > 0) {
    await new Promise((resolve) => {
      setTimeout(resolve, delay);
    });
  }
};

const authenticate = async (req, res, email, password, languageCode) => {
  if (await DeviceCookieService.isUserLocked(email)
        || (await DeviceCookieService.getCookieFromRequest(req) != null
        && (
          !(await DeviceCookieService.isValidDeviceCookie(req))
        || await DeviceCookieService.isDeviceLocked(req)
        ))
  ) {
    log('warn', 'Locked by device cookie verification.');
    throw new RequestException(ETranslation.EMAIL_OR_PASSWORD_INCORRECT);
  }

  let user = null;
  try {
    user = await UserRepository.findByEmail(email);
    if (!user) {
      log('info', 'User not found.');
      throw new RequestException(ETranslation.EMAIL_OR_PASSWORD_INCORRECT);
    }

    if (user.user_state !== EUserState.ACTIVE) {
      log('warn', 'User is not active.');
      throw new RequestException(ETranslation.EMAIL_OR_PASSWORD_INCORRECT);
    }

    const validPassword = await validatePasswordHash(password, user.password_hash);
    if (!validPassword) {
      log('info', "User password doesn't match.");
      throw new RequestException(ETranslation.EMAIL_OR_PASSWORD_INCORRECT);
    }
  } catch (err) {
    await DeviceCookieService.handleLoginFail(req, res, email);
    throw err;
  }

  // When LOGIN_OTP_VALIDATION is true, login requires OTP validation sent to email
  // Otherwise return the full token
  const token = await createAuthToken(user.id, process.env.LOGIN_OTP_VALIDATION !== EEnvironmentVariableValues.TRUE);
  if (process.env.LOGIN_OTP_VALIDATION === EEnvironmentVariableValues.TRUE) {
    await EmailService.sendOtpEmail(user.id, languageCode);
  }

  user.last_session_date = new Date();
  await UserRepository.updateUser(user);

  await DeviceCookieService.handleLoginSuccessful(req, res, email);

  return token;
};

export const authenticateWithRandomResponseTime = async (req, res, email, password, languageCode) => {
  const startTime = Date.now();

  try {
    const tokens = await authenticate(req, res, email, password, languageCode);
    await enforceRandomDelay(startTime);
    return tokens;
  } catch (error) {
    await enforceRandomDelay(startTime);
    throw error;
  }
};

export const validateOtp = async (otp) => {
  const userId = getStore().get(EAls.USER_ID);
  await validateOtpOrTokenByType(userId, EOtp.LOGIN, otp);

  return createAuthToken(userId, true);
};

export const refreshTokens = async () => {
  const userId = getStore().get(EAls.USER_ID);
  return createAuthToken(userId, true);
};

export const resendLoginOtp = async (languageCode) => {
  const userId = getStore().get(EAls.USER_ID);
  await EmailService.sendOtpEmail(userId, languageCode);
  return createAuthToken(userId, false);
};

export const resendValidateAccountOtp = async (req, res, email, languageCode) => {
  let isToSendOtp = true;
  let user = null;
  try {
    if (await DeviceCookieService.isUserLocked(email)
            || (await DeviceCookieService.getCookieFromRequest(req) != null
            && (
              !(await DeviceCookieService.isValidDeviceCookie(req))
                || await DeviceCookieService.isDeviceLocked(req)
            ))
    ) {
      log('warn', 'Locked by device cookie verification.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }

    user = await UserRepository.findByEmail(email);
    if (!user) {
      log('warn', 'User not found.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }

    if (user.user_state !== EUserState.PENDING_VALIDATION) {
      log('warn', 'User is not pending account validation.');
      throw new RequestException(ETranslation.MESSAGE_NOT_SENT);
    }
  } catch (err) {
    log('error', err.message);
    isToSendOtp = false;
  } finally {
    await DeviceCookieService.handleLoginFail(req, res, email);
  }

  if (isToSendOtp) {
    await EmailService.sendAccountValidationEmail(user.id, languageCode, process.env.VALIDATION_TYPE);
  }

  // allways return true in order to prevent user enumeration
  return true;
};
