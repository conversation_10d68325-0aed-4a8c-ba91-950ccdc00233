import { log } from '../logging/logger.js';
import { ETranslation } from '../constants/ETranslation.js';
import * as SubRoleRepository from '../database/repositories/subRoleRepository.js';
import * as FunctionalityRepository from '../database/repositories/functionalityRepository.js';
import * as SubRoleFunctionalityRepository from '../database/repositories/subRoleFunctionalityRepository.js';
import RequestException from '../exceptions/requestException.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import { EPermissionValue } from '../constants/EPermissionValue.js';
import { getSubRoleIdByUserId } from './roleService.js';
import { getPermission } from '../database/repositories/subRoleFunctionalityRepository.js';
import { EAls } from '../constants/EAls.js';
import { getStore } from '../utils/asyncLocalStorage.js';

export const getSubrolePermissions = async (subRoleId) => {
  const subrole = await SubRoleRepository.findById(subRoleId);
  if (!subrole) {
    log('warn', 'Subrole not found.');
    throw new RequestException(ETranslation.SUBROLE_NOT_FOUND);
  }
  return SubRoleFunctionalityRepository.findAllPermissionsBySubrole(subRoleId);
};

// Deletes subrole permissions and creates only the permissions passed in functionalities
export const updatePermissions = async (subRoleId, functionalities) => {
  const subrole = await SubRoleRepository.findById(subRoleId);
  if (!subrole) {
    log('warn', 'Subrole not found.');
    throw new RequestException(ETranslation.SUBROLE_NOT_FOUND);
  }

  // Deletes all permissions from subrole
  await SubRoleFunctionalityRepository.deleteAllSubroleFunctionalities(subRoleId);

  // Creates subrole permissions
  await Promise.all(functionalities.map(async (f) => {
    const functionality = await FunctionalityRepository.findByCode(f.code);

    // Check if functionality, permissionAction and permissionValue are valid
    if (functionality
      && Object.values(EPermissionAction).includes(f.permissionAction)
      && Object.values(EPermissionValue).includes(f.permissionValue)) {
      await SubRoleFunctionalityRepository.createSubRoleFunctionality(subRoleId, functionality.id, f.permissionAction, f.permissionValue);
    }
  }));
};

export const getPermissionValue = async (functionalityNeeded, action) => {
  const userId = getStore().get(EAls.USER_ID);
  const subRoles = await getSubRoleIdByUserId(userId);
  const subRoleId = subRoles.subrole_id;
  const functionality = await FunctionalityRepository.findByCode(functionalityNeeded);
  const permissionValueResult = await getPermission(subRoleId, functionality.id, action);
  const permissionValue = permissionValueResult.permission_value;
  return { value: permissionValue, userId };
};

export const createSubRoleWithPermissions = async (params) => {
  const subrole = await SubRoleRepository.findByCode(params.code);
  if (subrole.length > 0) {
    log('warn', 'SubRole already exists');
    throw new RequestException(ETranslation.SUBROLE_ALREADY_EXISTS);
  }
  const newSubRole = await SubRoleRepository.createSubRole(params.code, params.roleId);

  await Promise.all(params.permissions.map(async (f) => {
    const functionality = await FunctionalityRepository.findByCode(f.code);

    // Check if functionality, permissionAction and permissionValue are valid
    if (functionality
      && Object.values(EPermissionAction).includes(f.permissionAction)
      && Object.values(EPermissionValue).includes(f.permissionValue)) {
      await SubRoleFunctionalityRepository.createSubRoleFunctionality(newSubRole.id, functionality.id, f.permissionAction, f.permissionValue);
    }
  }));
};

export const duplicateSubRole = async (params) => {
  const subrole = await SubRoleRepository.findById(params.subroleId);
  if (!subrole) {
    log('warn', 'SubRole was not found');
    throw new RequestException(ETranslation.SUBROLE_NOT_FOUND);
  }
  const newSubRole = await SubRoleRepository.createSubRole(params.newCode, subrole.role_id);

  const permissions = await SubRoleFunctionalityRepository.findAllPermissionsBySubrole(params.subroleId);
  await Promise.all(
    permissions.map((permission) => SubRoleFunctionalityRepository.createSubRoleFunctionality(
      newSubRole.id,
      permission.functionality_id,
      permission.permission_action,
      permission.permission_value,
    )),
  );
};

export const deleteSubRole = async (subroleId) => {
  const subrole = await SubRoleRepository.findById(subroleId);
  if (!subrole) {
    log('warn', 'SubRole was not found');
    throw new RequestException(ETranslation.SUBROLE_NOT_FOUND);
  }

  await SubRoleFunctionalityRepository.deleteAllSubroleFunctionalities(subroleId);
  await SubRoleRepository.deleteSubRole(subroleId);
};
