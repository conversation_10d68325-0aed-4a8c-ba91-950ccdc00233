import * as OtpRepository from '../database/repositories/otpRepository.js';
import { generateRandomOtp, generateRandomToken } from '../utils/randomGeneratorUtil.js';
import { log } from '../logging/logger.js';
import { EOtp } from '../constants/EOtp.js';
import { ETranslation } from '../constants/ETranslation.js';
import RequestException from '../exceptions/requestException.js';
import { EValidationTypes } from '../constants/EValidationTypes.js';

export const addOtpOrTokenByType = async (userId, eOtp, newField = null) => {
  const otpExists = await OtpRepository.findByUserIdAndType(userId, eOtp);

  let previousNewField;
  if (otpExists) {
    previousNewField = otpExists.newField;
    await OtpRepository.deleteOtp(userId, eOtp);
  }
  let validationType = process.env.VALIDATION_TYPE;

  // Login validation only uses OTP
  if (eOtp === EOtp.LOGIN) {
    validationType = EValidationTypes.OTP;
  }

  let otpOrToken;
  switch (validationType) {
    case EValidationTypes.OTP || eOtp === EOtp.LOGIN:
      otpOrToken = generateRandomOtp();
      break;
    case EValidationTypes.TOKEN:
      otpOrToken = generateRandomToken();
      break;
    default: // Use token validation by default
      otpOrToken = generateRandomToken();
      break;
  }

  const otpCreated = await OtpRepository.createOtp(
    userId,
    eOtp,
    otpOrToken,
    new Date(Date.now() + (
      eOtp === EOtp.PENDING_VALIDATION
        ? parseInt(process.env.VALIDATION_ACCOUNT_EXPIRE.match(/\d+/)[0], 10) * 24 * 60 * 60 * 1000 // 5d → milissegundos
        : parseInt(process.env.JWT_PARTIAL_EXPIRE.match(/\d+/)[0], 10) * 60 * 1000 // 5m → milissegundos
    )),
    newField || previousNewField,
  );

  return otpCreated.otp;
};

export const validateOtpOrTokenByType = async (userId, eOtp, otp) => {
  const otpEntry = await OtpRepository.findByUserIdAndTypeAndOtp(userId, eOtp, otp);
  if (!otpEntry) {
    log('warn', 'Otp not found');
    throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
  }

  if (new Date() > new Date(otpEntry.expires_at)) {
    log('warn', 'Otp expired');
    await OtpRepository.deleteOtp(userId, eOtp);
    throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
  }

  await OtpRepository.deleteOtp(userId, eOtp);

  return true;
};
