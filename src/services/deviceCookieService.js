import { log } from '../logging/logger.js';
import * as DeviceCookieRepository from '../database/repositories/deviceCookieRepository.js';
import { generateRandomUuid } from '../utils/randomGeneratorUtil.js';
import { generateHmac } from '../utils/encryptionUtil.js';

const SPLITTER = '###';
const DEVICE_COOKIE_NAME = 'DEVICE_ID';

export const getCookieFromRequest = async (req) => {
  const cookieValue = req.cookies[DEVICE_COOKIE_NAME];
  if (!cookieValue) {
    return null;
  }
  return cookieValue;
};

export const createAndSetCookie = async (req, res, email) => {
  const cookieFromRequest = await getCookieFromRequest(req);

  if (cookieFromRequest) {
    const parts = cookieFromRequest.split(SPLITTER);
    if (parts[0] === email) {
      return cookieFromRequest;
    }
  }

  const nonce = generateRandomUuid();
  const data = email + SPLITTER + nonce;

  const signature = await generateHmac(data, process.env.DEVICE_COOKIE_SECRET_KEY);
  const deviceId = data + SPLITTER + signature;

  res.cookie(
    DEVICE_COOKIE_NAME,
    deviceId,
    {
      httpOnly: true,
      secure: true,
      sameSite: 'strict',
      path: '/',
      maxAge: 180 * 24 * 60 * 60 * 1000, // 6 meses em milissegundos
    },
  );

  return deviceId;
};

export const handleLoginSuccessful = async (req, res, email) => {
  const deviceId = await createAndSetCookie(req, res, email);

  const deviceCookie = await DeviceCookieRepository.findByEmail(email);
  if (deviceCookie) {
    deviceCookie.login_attempts = 1;
    deviceCookie.locked_by_attempts = false;
    await DeviceCookieRepository.updateDeviceCookie(deviceCookie);
  } else {
    await DeviceCookieRepository.createDeviceCookie(deviceId, email);
  }
};

export const handleLoginFail = async (req, res, email) => {
  const deviceId = await createAndSetCookie(req, res, email);

  const deviceCookie = await DeviceCookieRepository.findByEmail(email);
  if (deviceCookie) {
    deviceCookie.login_attempts += 1;
    log('debug', `Device cookie increased: ${deviceCookie.login_attempts}`);
    if (deviceCookie.login_attempts > process.env.DEVICE_COOKIE_MAX_LOGIN_ATTEMPTS) {
      log('warn', 'Device loocked by attempts.');
      deviceCookie.locked_by_attempts = true;
    }
    await DeviceCookieRepository.updateDeviceCookie(deviceCookie);
  } else {
    await DeviceCookieRepository.createDeviceCookie(deviceId, email);
  }
};

const constantTimeCompare = (a, b) => {
  if (!a || !b || a.length !== b.length) {
    return false;
  }

  let result = 0;

  for (let i = 0; i < a.length; i += 1) {
    // eslint-disable-next-line no-bitwise
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
};

export const isValidDeviceCookie = async (req, email = null) => {
  const cookie = await getCookieFromRequest(req);
  if (!cookie) {
    return true;
  }

  const parts = cookie.split(SPLITTER);
  if (parts.length !== 3) {
    return false;
  }

  const login = parts[0];
  const nonce = parts[1];
  const signature = parts[2];

  const expectedSignature = await generateHmac(login + SPLITTER + nonce, process.env.DEVICE_COOKIE_SECRET_KEY);

  const isValid = expectedSignature != null
        && constantTimeCompare(signature, expectedSignature)
        && (email == null || login === email);

  log('debug', `Is valid device cookie: ${isValid}`);
  return isValid;
};

export const isDeviceLocked = async (req) => {
  const cookie = await getCookieFromRequest(req);
  const deviceCookie = await DeviceCookieRepository.findByCookie(cookie);

  const locked = deviceCookie != null && deviceCookie.locked_by_attempts;
  log('debug', `Is device locked: ${locked}`);
  return locked;
};

export const isUserLocked = async (email) => {
  const deviceCookie = await DeviceCookieRepository.findByEmail(email);
  const locked = deviceCookie != null && deviceCookie.locked_by_attempts;
  log('debug', `Is user locked: ${locked}`);
  return locked;
};

export const clearCookie = async (res) => {
  res.clearCookie(DEVICE_COOKIE_NAME, {
    httpOnly: true,
    secure: true,
    sameSite: 'strict',
    path: '/',
  });
};
