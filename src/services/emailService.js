import { EmailClient } from '@azure/communication-email';
import * as <PERSON><PERSON>mailer from 'nodemailer';
import { log } from '../logging/logger.js';
import { EOtp } from '../constants/EOtp.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import { getTextFromTemplate } from './keyService.js';
import { getTranslationByCodeAndLanguageCode } from '../database/repositories/translationRepository.js';
import { ETranslation } from '../constants/ETranslation.js';
import * as OtpRepository from '../database/repositories/otpRepository.js';
import RequestException from '../exceptions/requestException.js';
import { EValidationTypes } from '../constants/EValidationTypes.js';

let emailClient;
switch (process.env.EMAIL_SERVICE) {
  case 'gmail': // gmail and sendinblue connect in the same way
  case 'sendinblue':
    emailClient = Nodemailer.createTransport({
      service: process.env.EMAIL_SERVICE,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
    break;
  case 'azure':
    emailClient = new EmailClient(process.env.EMAIL_ENDPOINT);
    break;
  default:
    log('error', 'Email service is not supported. Possible values for EMAIL_SERVICE in .env: azure, gmail and sendinblue');
}

const sendEmail = async (to, subject, text) => {
  switch (process.env.EMAIL_SERVICE) {
    case 'gmail': // gmail and sendinblue connect in the same way
    case 'sendinblue': {
      const mailOptions = {
        from: process.env.EMAIL_SENDER,
        to,
        subject,
        html: text,
      };
      await emailClient.sendMail(mailOptions);
      log('info', `Email sent to ${to}`);
    }
      break;
    case 'azure': {
      const emailMessage = {
        senderAddress: process.env.EMAIL_SENDER,
        content: {
          subject,
          html: text,
        },
        recipients: {
          to: [
            {
              address: to,
            },
          ],
        },
      };

      const poller = await emailClient.beginSend(emailMessage);
      const response = await poller.pollUntilDone();

      log('info', `Email ${response.id} sent to ${to}`);
    }
      break;
    default:
      log('error', 'Email service is not supported. Possible values for EMAIL_SERVICE in .env: azure, gmail and sendinblue. Email was not sent.');
  }
};

export const sendOtpEmail = async (userId, languageCode) => {
  const user = await UserRepository.findById(userId);
  const to = user.email;

  const subject = (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_SUBJECT_LOGIN_OTP, languageCode)).translated_text;
  const text = await getTextFromTemplate(
    (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_LOGIN_OTP, languageCode)).translated_text,
    userId,
  );

  await sendEmail(to, subject, text);
};

export const sendRecoverPasswordEmail = async (userId, languageCode, validationType) => {
  const user = await UserRepository.findById(userId);
  const to = user.email;

  const subject = (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_SUBJECT_RECOVER_PASSWORD, languageCode)).translated_text;
  let text = '';
  switch (validationType) {
    case EValidationTypes.OTP:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_RECOVER_PASSWORD_OTP, languageCode)).translated_text,
        userId,
      );
      break;
    case EValidationTypes.TOKEN:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN, languageCode)).translated_text,
        userId,
      );
      break;
    default: // Use token validation by default
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_RECOVER_PASSWORD_TOKEN, languageCode)).translated_text,
        userId,
      );
  }

  await sendEmail(to, subject, text);
};

export const sendAccountValidationEmail = async (userId, languageCode, validationType) => {
  const user = await UserRepository.findById(userId);
  const to = user.email;

  const subject = (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_SUBJECT_ACCOUNT_VALIDATION, languageCode)).translated_text;
  let text = '';
  switch (validationType) {
    case EValidationTypes.OTP:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_OTP, languageCode)).translated_text,
        userId,
      );
      break;
    case EValidationTypes.TOKEN:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN, languageCode)).translated_text,
        userId,
      );
      break;
    default: // Use token validation by default
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN, languageCode)).translated_text,
        userId,
      );
  }
  await sendEmail(to, subject, text);
};

export const sendUpdateEmailOtpEmail = async (userId, newEmail, languageCode, validationType) => {
  const subject = (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, languageCode)).translated_text;
  let text = '';
  switch (validationType) {
    case EValidationTypes.OTP:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP, languageCode)).translated_text,
        userId,
        newEmail,
      );
      break;
    case EValidationTypes.TOKEN:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN, languageCode)).translated_text,
        userId,
        newEmail,
      );
      break;
    default: // Use token validation by default
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN, languageCode)).translated_text,
        userId,
        newEmail,
      );
  }

  await sendEmail(newEmail, subject, text);
};

export const resendUpdateEmailOtpEmail = async (userId, languageCode, validationType) => {
  const oldOtp = await OtpRepository.findByUserIdAndType(userId, EOtp.UPDATE_EMAIL);
  if (!oldOtp) {
    log('info', 'Otp validation failed.');
    throw new RequestException(ETranslation.OTP_VALIDATION_FAILED);
  }
  const to = oldOtp.newField;

  const subject = (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_SUBJECT_UPDATE_EMAIL_OTP, languageCode)).translated_text;

  let text = '';
  switch (validationType) {
    case EValidationTypes.OTP:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_OTP, languageCode)).translated_text,
        userId,
        to,
      );
      break;
    case EValidationTypes.TOKEN:
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN, languageCode)).translated_text,
        userId,
        to,
      );
      break;
    default: // Use token validation by default
      text = await getTextFromTemplate(
        (await getTranslationByCodeAndLanguageCode(ETranslation.EMAIL_BODY_UPDATE_EMAIL_TOKEN, languageCode)).translated_text,
        userId,
        to,
      );
  }

  await sendEmail(to, subject, text);
};
