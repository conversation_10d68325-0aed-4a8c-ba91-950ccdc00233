import { EOtp } from '../constants/EOtp.js';
import { Ekey } from '../constants/EKey.js';
import { addOtpOrTokenByType } from './otpService.js';
import { findRolesByUserId } from '../database/repositories/roleRepository.js';
import { ERole } from '../constants/ERole.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import { DEFAULT_LANGUAGE } from '../constants/ELanguage.js';

const getLanguageCodeOfUser = async (userId) => {
  // If user is not logged in return default language
  if (!userId) {
    return DEFAULT_LANGUAGE;
  }
  const user = await UserRepository.findById(userId);
  const language = await user.getSelectedLanguage();
  return language.code;
};

export const getKeyValue = async (key, objectId, newField = null) => {
  switch (key) {
    case Ekey.LOGIN_OTP:
      return addOtpOrTokenByType(objectId, EOtp.LOGIN);
    case Ekey.RECOVER_PASSWORD_OTP:
      return addOtpOrTokenByType(objectId, EOtp.RECOVER_PASSWORD);
    case Ekey.ACCOUNT_VALIDATION_OTP:
      return addOtpOrTokenByType(objectId, EOtp.PENDING_VALIDATION);
    case Ekey.EMAIL_UPDATE_OTP:
      return addOtpOrTokenByType(objectId, EOtp.UPDATE_EMAIL, newField);
    case Ekey.BASE_URL: {
      // Return different url based on user role
      const isInternalUser = (await findRolesByUserId(objectId)).some((x) => x.code === ERole.INTERNAL_USER);
      return isInternalUser ? process.env.BACKOFFICE_URL : process.env.FRONTOFFICE_URL;
    }
    case Ekey.SUPPORT_EMAIL:
      return process.env.SUPPORT_EMAIL || '';
    case Ekey.RECOVER_PASSWORD_LINK: {
      const token = await addOtpOrTokenByType(objectId, EOtp.RECOVER_PASSWORD);
      const languageCode = await getLanguageCodeOfUser(objectId);
      // Return different url based on user role
      const isInternalUser = (await findRolesByUserId(objectId)).some((x) => x.code === ERole.INTERNAL_USER);
      return isInternalUser
        ? `${process.env.BACKOFFICE_URL}/${languageCode}/reset-password/token=${token}`
        : `${process.env.FRONTOFFICE_URL}/${languageCode}/reset-password/token=${token}`;
    }
    case Ekey.ACTIVATE_ACCOUNT_LINK: {
      const token = await addOtpOrTokenByType(objectId, EOtp.PENDING_VALIDATION);
      const languageCode = await getLanguageCodeOfUser(objectId);
      // Return different url based on user role
      const isInternalUser = (await findRolesByUserId(objectId)).some((x) => x.code === ERole.INTERNAL_USER);
      return isInternalUser
        ? `${process.env.BACKOFFICE_URL}/${languageCode}/activate/token=${token}`
        : `${process.env.FRONTOFFICE_URL}/${languageCode}/activate/token=${token}`;
    }
    case Ekey.EMAIL_UPDATE_LINK: {
      const token = await addOtpOrTokenByType(objectId, EOtp.UPDATE_EMAIL, newField);
      const languageCode = await getLanguageCodeOfUser(objectId);
      // Return different url based on user role
      const isInternalUser = (await findRolesByUserId(objectId)).some((x) => x.code === ERole.INTERNAL_USER);
      return isInternalUser
        ? `${process.env.BACKOFFICE_URL}/${languageCode}/validate/token=${token}`
        : `${process.env.FRONTOFFICE_URL}/${languageCode}/validate/token=${token}`;
    }
    default:
      throw new Error(`Invalid key: ${key}`);
  }
};

export const getTextFromTemplate = async (template, objectId, newField = null) => {
  let result = template;

  const keysToReplace = Object.values(Ekey).filter((key) => result.includes(key));
  const values = await Promise.all(
    keysToReplace.map((key) => getKeyValue(key, objectId, newField)),
  );

  keysToReplace.forEach((key, i) => {
    result = result.replaceAll(key, values[i]);
  });

  return result;
};
