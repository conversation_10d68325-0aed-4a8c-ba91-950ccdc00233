import * as SubRoleRepository from '../database/repositories/subRoleRepository.js';
import * as UserSubRoleRepository from '../database/repositories/user_subRoleRepository.js';
import * as RoleRepository from '../database/repositories/roleRepository.js';
import { findById } from '../database/repositories/userRepository.js';
// import { getUser } from './userService.js';
import { log } from '../logging/logger.js';
import { ETranslation } from '../constants/ETranslation.js';
import RequestException from '../exceptions/requestException.js';

export const addSubRoleOfUser = async (userId, subRoleCode, roleCode) => {
  const role = await RoleRepository.findByCode(roleCode);
  if (!role) {
    log('error', 'Role not found');
    throw new RequestException(ETranslation.ROLE_NOT_FOUND);
  }

  const subRole = await SubRoleRepository.findBySubRoleCodeAndRoleId(subRoleCode, role.id);
  if (!subRole) {
    log('error', 'SubRole not found');
    throw new RequestException(ETranslation.SUBROLE_NOT_FOUND);
  }
  const subRoles = await SubRoleRepository.findSubRolesByUserId(userId);

  const exists = await Promise
    .all(subRoles.map(async (sr) => {
      const srRole = await sr.getRole();
      return sr.code === subRoleCode && srRole.code === roleCode;
    }))
    .then((results) => results.includes(true));

  if (exists) {
    log('info', 'SubRole already set on the user');
    return;
  }
  try {
    if (!userId || !subRole?.id) {
      log('error', `Invalid parameters: userId=${userId}, subRoleId=${subRole?.id}`);
      throw new RequestException(ETranslation.INVALID_PARAMETERS);
    }

    await UserSubRoleRepository.addSubRoleOfUser(userId, subRole.id);

    log('info', `SubRole (id: ${subRole.id}) successfully assigned to user (id: ${userId})`);
  } catch (error) {
    log('error', `Failed to add SubRole to user: ${error.message}`, error);

    if (error.name === 'SequelizeValidationError') {
      log('debug', `Validation error details: ${JSON.stringify(error.errors)}`);
      throw new RequestException(ETranslation.INVALID_PARAMETERS);
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new RequestException(ETranslation.SUBROLE_ALREADY_ASSIGNED);
    }

    throw new RequestException(ETranslation.SOMETHING_WENT_WRONG);
  }
};

export const removeSubRolesOfUser = async (userId) => {
  const subRolesOfUser = (await SubRoleRepository.findSubRolesByUserId(userId));
  if (!subRolesOfUser) {
    log('info', 'User SubRole not found');
    return;
  }
  const subRoleIds = subRolesOfUser.map((subRole) => subRole.id);
  await UserSubRoleRepository.deleteSubRolesOfUser(userId, subRoleIds);
};

export const manageSubRolesOfUser = async (userId, subRoleCode, roleCode) => {
  await removeSubRolesOfUser(userId);
  await addSubRoleOfUser(userId, subRoleCode, roleCode);
};

export const getSubRoleIdByUserId = async (userId) => {
  const user = await findById(userId);
  if (!user) {
    log('warn', 'user was not found');
    throw new RequestException(ETranslation.USER_NOT_FOUND);
  }
  return UserSubRoleRepository.findSubRoleIdsByUserId(userId);
};
