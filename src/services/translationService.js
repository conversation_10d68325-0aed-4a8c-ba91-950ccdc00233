import fs from 'fs/promises';
import { createObjectCsvStringifier } from 'csv-writer';
import { ELanguage } from '../constants/ELanguage.js';
import * as TranslationRepository from '../database/repositories/translationRepository.js';
import * as LanguageRepository from '../database/repositories/languageRepository.js';
import { log } from '../logging/logger.js';
import { ETranslation } from '../constants/ETranslation.js';
import RequestException from '../exceptions/requestException.js';

export const addTranslations = async (translations) => {
  await Promise.all(translations.flatMap(
    ({ languageCode, list }) => list.map(({ key, value }) => TranslationRepository.addTranslation(languageCode, key, value)),
  ));
};

export const getTranslationCsv = async (languageCode) => {
  if (languageCode === ELanguage.EN) {
    const translations = (await TranslationRepository.getTranslationsByLanguageCode(languageCode))
      .sort((a, b) => a.code.localeCompare(b.code));

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'code', title: 'Code (do not change this column)' },
        { id: 'translated_text', title: 'English translation' },
      ],
      fieldDelimiter: ',', // standard CSV
      alwaysQuote: true, // <- this ensures values are quoted
    });

    const csvContent = csvStringifier.getHeaderString()
            + csvStringifier.stringifyRecords(translations);

    return csvContent;
  }

  const translationsEn = await TranslationRepository.getTranslationsByLanguageCode(ELanguage.EN);
  const translations = await TranslationRepository.getTranslationsByLanguageCode(languageCode);

  // Map translations by code for fast lookup
  const englishTranslationsMap = Object.fromEntries(
    translationsEn.map((t) => [t.code, t.translated_text]),
  );

  const combined = translations
    .sort((a, b) => a.code.localeCompare(b.code))
    .map((t) => ({
      code: t.code,
      translated_en: englishTranslationsMap[t.code] || '',
      translated_local: t.translated_text,
    }));

  const csvStringifier = createObjectCsvStringifier({
    header: [
      { id: 'code', title: 'Code (do not change this column)' },
      { id: 'translated_en', title: 'English translation (do not change this column)' },
      { id: 'translated_local', title: `translated_${languageCode}` },
    ],
    fieldDelimiter: ',', // standard CSV
    alwaysQuote: true, // <- this ensures values are quoted
  });

  const csvContent = csvStringifier.getHeaderString()
            + csvStringifier.stringifyRecords(combined);

  return csvContent;
};

const parseTranslationsCsv = async (filePath) => {
  const data = await fs.readFile(filePath, 'utf-8');

  const lines = data.split('\n').filter((line) => line.trim() !== '');

  const rows = lines.map((line) => {
    const row = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i += 1) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === '"' && inQuotes && nextChar === '"') {
        current += '"';
        i += 1; // Skip next quote
      } else if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        row.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    row.push(current.trim());
    return row;
  });

  return rows;
};

export const updateTranslationsCsv = async (languageCode, files) => {
  if (files.length === 0) {
    log('error', 'No CSV file uploaded.');
    throw new RequestException(ETranslation.MISSING_CSV_FILE);
  }
  if (files.length > 1) {
    log('error', 'Cannot upload more than 1 file.');
    throw new RequestException(ETranslation.MULTIPLE_CSV_FILES_UPLOADED);
  }
  const language = await LanguageRepository.getLanguageByCode(languageCode);
  if (!language) {
    throw new RequestException(ETranslation.LANGUAGE_NOT_FOUND);
  }

  const updates = [];
  const file = files[0];
  const [, ...rows] = await parseTranslationsCsv(file.path);

  if (languageCode === ELanguage.EN) {
    rows.forEach((row) => {
      if (row[0] && row[1]) {
        updates.push({ code: row[0], translated_text: row[1] });
      }
    });
  } else {
    rows.forEach((row) => {
      if (row[0] && row[2]) {
        updates.push({ code: row[0], translated_text: row[2] });
      }
    });
  }

  await TranslationRepository.updateTranslationsBulk(language.id, updates);
};

export const createTemplateCsv = async () => {
  const translationsEn = await TranslationRepository.getTranslationsByLanguageCode(ELanguage.EN);
  const distinctCodes = await TranslationRepository.getDistinctTranslationCodes();

  // Map translations by code for fast lookup
  const englishTranslationsMap = Object.fromEntries(translationsEn.map((t) => [t.code, t.translated_text]));

  const combined = distinctCodes.map((t) => ({
    code: t.code,
    translated_en: englishTranslationsMap[t.code] || '',
    translated_new: '',
  }));

  const csvStringifier = createObjectCsvStringifier({
    header: [
      { id: 'code', title: 'Code (do not change this column)' },
      { id: 'translated_en', title: 'English translation (do not change this column)' },
      { id: 'translated_new', title: 'New translation' },
    ],
  });

  const csvContent = csvStringifier.getHeaderString()
            + csvStringifier.stringifyRecords(combined);

  return csvContent;
};
