import express from 'express';
import {
  getModulesBySubRole, getSubRolePermissions, updateSubRolePermissions, createSubRole, duplicateSubRole, deleteSubRole,
} from '../controllers/permissionsController.js';
import { validatePermissions } from '../middlewares/validatePermissions.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';

const router = express.Router();

router.get('', getModulesBySubRole);

router.get(
  '/:uid',
  validatePermissions([{ code: EFunctionality.PERMISSION_MANAGEMENT.code, action: EPermissionAction.READ }]),
  getSubRolePermissions,
);

router.put(
  '/:uid',
  validatePermissions([{ code: EFunctionality.PERMISSION_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  updateSubRolePermissions,
);

/**
 * @swagger
 * /api/v1/permissions/subrole:
 *   post:
 *     summary: Create SubRole
 *     tags:
 *       - Permissions
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - roleId
 *               - permissions
 *             properties:
 *               code:
 *                 type: string
 *                 example: ADMIN
 *               roleId:
 *                 type: string
 *                 format: uuid
 *                 example: 03e0f229-b4c2-4f11-a3a7-2d1ef782a1cd
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - code
 *                     - permissionAction
 *                     - permissionValue
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: USER_MANAGEMENT
 *                     permissionAction:
 *                       type: string
 *                       example: CREATE
 *                     permissionValue:
 *                       type: string
 *                       example: ALLOWED
 *     responses:
 *       200:
 *         description: Permissions updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Permissions updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: ADMIN
 *                     roleId:
 *                       type: string
 *                       format: uuid
 *                       example: 03e0f229-b4c2-4f11-a3a7-2d1ef782a1cd
 *                     permissions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           code:
 *                             type: string
 *                             example: USER_MANAGEMENT
 *                           permissionAction:
 *                             type: string
 *                             example: CREATE
 *                           permissionValue:
 *                             type: string
 *                             example: ALLOWED
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/subrole', validatePermissions([{ code: EFunctionality.PERMISSION_MANAGEMENT.code, action: EPermissionAction.CREATE }]), createSubRole);

/**
 * @swagger
 * /api/v1/permissions/duplicate-subrole:
 *   post:
 *     summary: Duplicate a Sub Role with a new code
 *     tags:
 *       - SubRoles
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - subroleId
 *               - newCode
 *             properties:
 *               subroleId:
 *                 type: string
 *                 format: uuid
 *                 example: "c3b045d0-8f21-4d39-8e94-867a30e91f6c"
 *               newCode:
 *                 type: string
 *                 example: "newSubRoleCode"
 *     responses:
 *       201:
 *         description: Sub Role duplicated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Sub Role duplicated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     code:
 *                       type: string
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/duplicate-subrole',
  validatePermissions([{ code: EFunctionality.PERMISSION_MANAGEMENT.code, action: EPermissionAction.CREATE }]),
  duplicateSubRole,
);

/**
 * @swagger
 * /api/v1/permissions/subrole/{uid}:
 *   delete:
 *     summary: Delete a Sub Role by ID
 *     tags:
 *       - SubRoles
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: uid
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Unique identifier of the SubRole to delete
 *     responses:
 *       200:
 *         description: Sub Role deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Sub Role deleted successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Sub Role not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/subrole/:uid',
  validatePermissions([{ code: EFunctionality.PERMISSION_MANAGEMENT.code, action: EPermissionAction.DELETE }]),
  deleteSubRole,
);
export default router;
