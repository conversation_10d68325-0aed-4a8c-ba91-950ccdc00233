import express from 'express';
import {
  getUsers, getUserStates, getUserById, updateOtherUser, getAuthUser, updateOwnUser,
  validateEmailOtp, resendUpdateEmailOtp, addUser, resetPassword,
  logout, getUserSubroles,
} from '../controllers/usersController.js';
import { validatePermissions } from '../middlewares/validatePermissions.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';

const router = express.Router();

router.get('/list', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getUsers);

router.get('/user-states', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getUserStates);

router.post('/email/validate-otp', validateEmailOtp);

router.post('/email/resend-otp', resendUpdateEmailOtp);

router.post('/logout', logout);

router.get('/subroles', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getUserSubroles);

router.get('/:user_id', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getUserById);

router.put('/:user_id', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]), updateOtherUser);

router.post('', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.CREATE }]), addUser);

router.get('', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getAuthUser);

router.put('', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]), updateOwnUser);

/**
 * @swagger
 * /api/v1/users/password-reset:
 *   post:
 *     summary: Admin resets the password for another user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: languageCode
 *         schema:
 *           type: string
 *         required: false
 *         description: Optional language code (e.g. 'pt', 'en')
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *     responses:
 *       200:
 *         description: Password recovery email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Recovery email sent
 *       401:
 *         description: Error resetting password
 */
router.post(
  '/password-reset',
  validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  resetPassword,
);
export default router;
