import express from 'express';
import {
  addBranch,
  addTeam, areUsersInSameTeam, associateUsersToTeam, getBranches, getTeams, getUsersByTeamId, removeUserFromTeam, updateBranch, updateTeam,
} from '../controllers/teamController.js';
import { validatePermissions } from '../middlewares/validatePermissions.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';

const router = express.Router();
/**
 * @swagger
 * /api/v1/teams/branches:
 *   get:
 *     summary: Get Branches to relate with Teams
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *           format: uuid
 *         required: false
 *         description: ID of the branch
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         required: false
 *         description: Name of the branch
 *     responses:
 *       200:
 *         description: Branches returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Branches retrieved successfully
 *       401:
 *         description: Error returning branches
 */
router.get('/branches', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getBranches);
/**
 * @swagger
 * /api/v1/teams/:
 *   get:
 *     summary: Get Teams
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter team by ID
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter teams by name
 *       - in: query
 *         name: branch
 *         schema:
 *           type: string
 *         description: Filter teams by branch name
 *     responses:
 *       200:
 *         description: Teams returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Teams returned successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                       branch:
 *                         type: string
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getTeams);
/**
 * @swagger
 * /api/v1/teams:
 *   post:
 *     summary: Create a new Team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - branch
 *             properties:
 *               name:
 *                 type: string
 *                 example: Departamento Comercial
 *               branch:
 *                 type: string
 *                 example: EVORA
 *     responses:
 *       201:
 *         description: Team created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Team created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: 6a94382f-2de2-4b6c-b4fa-2d4e85b25dcb
 *                     name:
 *                       type: string
 *                       example: Departamento Comercial
 *                     branch:
 *                       type: string
 *                       example: EVORA
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.CREATE }]), addTeam);
/**
 * @swagger
 * /api/v1/teams/branch:
 *   post:
 *     summary: Create a new Branch
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - branch_id
 *             properties:
 *               name:
 *                 type: string
 *                 example: EVORA
 *               branch_id:
 *                 type: string
 *                 format: uuid
 *                 example: "4e89c264-75d0-4db2-b61f-1d06304fb308"
 *     responses:
 *       201:
 *         description: Team created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Branch created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/branch', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.CREATE }]), addBranch);
/**
 * @swagger
 * /api/v1/teams:
 *   put:
 *     summary: Update a Team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         required: false
 *         schema:
 *           type: string
 *         description: New name for the team
 *       - in: query
 *         name: branch
 *         required: false
 *         schema:
 *           type: string
 *         description: New branch name to associate with the team
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               team_id:
 *                 type: string
 *                 format: uuid
 *                 example: 123e4567-e89b-12d3-a456-************
 *     responses:
 *       200:
 *         description: Team updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Team updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                     branch:
 *                       type: string
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]), updateTeam);
/**
 * @swagger
 * /api/v1/teams/branch:
 *   put:
 *     summary: Update a Branch
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         required: false
 *         schema:
 *           type: string
 *         description: New name for the team
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               branch_id:
 *                 type: string
 *                 format: uuid
 *                 example: 123e4567-e89b-12d3-a456-************
 *     responses:
 *       200:
 *         description: Team updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Branch updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/branch', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]), updateBranch);
/**
 * @swagger
 * /api/v1/teams/associate-users-team:
 *   post:
 *     summary: Associate users to a team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - team_id
 *               - user_ids
 *             properties:
 *               team_id:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the team
 *                 example: 123e4567-e89b-12d3-a456-************
 *               user_ids:
 *                 type: array
 *                 description: Array of user UUIDs to associate with the team
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example:
 *                   - 123e4567-e89b-12d3-a456-************
 *                   - 987f6543-e21c-34d5-b678-************
 *     responses:
 *       200:
 *         description: Users successfully associated with the team
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Users associated with team successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/associate-users-team',
  validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  associateUsersToTeam,
);
/**
 * @swagger
 * /api/v1/teams/same-team:
 *   post:
 *     summary: Check if two users belong to the same team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - otherUserId
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               otherUserId:
 *                 type: string
 *                 format: uuid
 *                 example: "987e6543-e21c-45d6-b321-123456789abc"
 *     responses:
 *       200:
 *         description: Check completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Users belong to the same team
 *                 data:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/same-team', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), areUsersInSameTeam);
/**
 * @swagger
 * /api/v1/teams/get-users/{team_id}:
 *   get:
 *     summary: Get users from a specific team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: team_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: UUID of the team to fetch users for
 *     responses:
 *       200:
 *         description: Users returned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Users returned successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: array
 *                       items:
 *                         type: object
 *                         description: User DTO object
 *                     total:
 *                       type: integer
 *                       example: 5
 *       400:
 *         description: Invalid team ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Team not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/get-users/:team_id',
  validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]),
  getUsersByTeamId,
);
/**
 * @swagger
 * /api/v1/teams/remove-user-team:
 *   delete:
 *     summary: Remove a user from a team
 *     tags:
 *       - Teams
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - team_id
 *               - user_id
 *             properties:
 *               team_id:
 *                 type: string
 *                 format: uuid
 *                 description: UUID of the team
 *                 example: 123e4567-e89b-12d3-a456-************
 *               user_id:
 *                 type: string
 *                 format: uuid
 *                 description: UUID of the user
 *                 example: 987f6543-e21c-34d5-b678-************
 *     responses:
 *       200:
 *         description: User successfully removed from the team
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User removed from team successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User or team not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/remove-user-team',
  validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.DELETE }]),
  removeUserFromTeam,
);
export default router;
