import express from "express";
import userRoutes from "./userRoutes.js";
import authRoutes from "./authRoutes.js";
import translationRoutes from "./translationRoutes.js";
import workplaceRoutes from "./workplaceRoutes.js";
import fileRoutes from "./fileRoutes.js";
import teamRoutes from "./teamRoutes.js";
import permissionRoutes from "./permissionRoutes.js";
import healthRoutes from "./healthRoutes.js";
import { validateAuthentication } from "../middlewares/authMiddleware.js";
import { ETokenType } from "../constants/ETokenType.js";

const router = express.Router();

router.use("/v1", healthRoutes);
router.use("/v1/auth", authRoutes);
router.use(
  "/v1/users",
  validateAuthentication(ETokenType.AUTH_FULL),
  userRoutes
);
router.use("/v1/translations", translationRoutes);
router.use(
  "/v1/workplaces",
  validateAuthentication(ETokenType.AUTH_FULL),
  workplaceRoutes
);
router.use(
  "/v1/files",
  validateAuthentication(ETokenType.AUTH_FULL),
  fileRoutes
);
router.use(
  "/v1/teams",
  validateAuthentication(ETokenType.AUTH_FULL),
  teamRoutes
);
router.use(
  "/v1/permissions",
  validateAuthentication(ETokenType.AUTH_FULL),
  permissionRoutes
);

router.use(
  "/v1/leads",
  validateAuthentication(ETokenType.AUTH_FULL),
  permissionRoutes
);

export default router;
