import express from 'express';
import {
  login, register, getRecoverPasswordEmail, recoverPassword, validateAccount, resendValidateAccountOtp,
  validateOtp, refreshTokens, resendLoginOtp,
} from '../controllers/authController.js';
import { validateAuthentication } from '../middlewares/authMiddleware.js';
import { ETokenType } from '../constants/ETokenType.js';

const router = express.Router();

router.post('/login', login);

/**
 * @swagger
* /validate-otp:
*   post:
*     summary: Valida um código OTP
*     description: Valida um código OTP enviado ao utilizador
*     security:
*       - bearerAuth: []
*     requestBody:
*       required: true
*       content:
*         application/json:
*           schema:
*             type: object
*             required:
*               - otp
*             properties:
*               otp:
*                 type: string
*                 example: "123456"
*     responses:
*       200:
*         description: OTP validado com sucesso
*         content:
*           application/json:
*             example:
*               accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
*               refreshToken: "dGhpc2lzYXJlZnJlc2h0b2tlbg"
*       400:
*         description: Dados inválidos
*         content:
*           application/json:
*             example:
*               error: "otp is required"
*       401:
*         description: OTP inválido
*         content:
*           application/json:
*             example:
*               error: "Invalid OTP"
*       500:
*         description: Erro no servidor
*         content:
*           application/json:
*             example:
*               error: "Internal server error"
*/
router.post('/validate-otp', validateAuthentication(ETokenType.AUTH_PARTIAL), validateOtp);

/**
 * @swagger
 * /refresh:
 *   get:
 *     summary: Renova os tokens de autenticação
 *     description: Renova o accessToken usando o refreshToken
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Tokens renovados com sucesso
 *         content:
 *           application/json:
 *             example:
 *               accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
 *               refreshToken: "dGhpc2lzYXJlZnJlc2h0b2tlbg"
 *       401:
 *         description: Token inválido ou expirado
 *         content:
 *           application/json:
 *             example:
 *               error: "Invalid refresh token"
 *       500:
 *         description: Erro no servidor
 *         content:
 *           application/json:
 *             example:
 *               error: "Internal server error"
 */
router.get('/refresh', validateAuthentication(ETokenType.AUTH_REFRESH), refreshTokens);

/**
 * @swagger
* /resend-login-otp:
*   post:
*     summary: Reenvia o OTP de login
*     description: Reenvia o OTP de login e retorna um novo tokenPartial
*     requestBody:
*       required: true
*       content:
*         application/json:
*           schema:
*             type: object
*             required:
*               - tokenPartial
*             properties:
*               tokenPartial:
*                 type: string
*                 example: "partialToken123"
*     responses:
*       200:
*         description: OTP reenviado com sucesso
*         content:
*           application/json:
*             example:
*               tokenPartial: "newPartialToken456"
*       400:
*         description: Dados inválidos
*         content:
*           application/json:
*             example:
*/
router.post('/resend-login-otp', validateAuthentication(ETokenType.AUTH_PARTIAL), resendLoginOtp);

router.post('/register', register);

router.post('/send-recover-password', getRecoverPasswordEmail);

router.post('/recover-password', recoverPassword);

router.post('/validate-account', validateAccount);

router.post('/resend-validate-account-otp', resendValidateAccountOtp);

export default router;
