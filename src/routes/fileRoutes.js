import express from 'express';
import { upload, multerWithContext } from '../utils/storage.js';
import {
  deleteFile, downloadFile, listFiles, uploadFile,
} from '../controllers/fileController.js';

const router = express.Router();

router.post(
  '',
  multerWithContext(upload.any())(uploadFile),
);

router.get(
  '',
  listFiles,
);

router.get(
  '/:uid',
  downloadFile,
);

router.delete(
  '/:uid',
  deleteFile,
);

export default router;
