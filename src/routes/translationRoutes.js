import express from 'express';
import {
  getTranslationsCsvByLanguage, updateTranslationsWithCsv, getTemplateCsv, getTranslationsByLanguage,
  getAllLanguages,
} from '../controllers/translationsController.js';
import { validatePermissions } from '../middlewares/validatePermissions.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { upload, multerWithContext } from '../utils/storage.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import { ETokenType } from '../constants/ETokenType.js';
import { validateAuthentication } from '../middlewares/authMiddleware.js';

const router = express.Router();

router.get('/languages', getAllLanguages);

router.get('/:languageCode', getTranslationsByLanguage);

router.get(
  '/csv/template',
  validateAuthentication(ETokenType.AUTH_FULL),
  validatePermissions([{ code: EFunctionality.LANGUAGE_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  getTemplateCsv,
);

router.get(
  '/csv/:languageCode',
  validateAuthentication(ETokenType.AUTH_FULL),
  validatePermissions([{ code: EFunctionality.LANGUAGE_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  getTranslationsCsvByLanguage,
);

router.put(
  '/csv/:languageCode',
  validateAuthentication(ETokenType.AUTH_FULL),
  validatePermissions([{ code: EFunctionality.LANGUAGE_MANAGEMENT.code, action: EPermissionAction.UPDATE }]),
  multerWithContext(upload.any())(updateTranslationsWithCsv),
);

// TODO: Add route to add new language with CSV including all translations

export default router;
