import express from 'express';
import {
  getLeads, addLead,
} from '../controllers/leadsController.js';
import { validatePermissions } from '../middlewares/validatePermissions.js';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';

const router = express.Router();

router.get('/list', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.READ }]), getLeads);

router.post('', validatePermissions([{ code: EFunctionality.USER_MANAGEMENT.code, action: EPermissionAction.CREATE }]), addLead);

export default router;
