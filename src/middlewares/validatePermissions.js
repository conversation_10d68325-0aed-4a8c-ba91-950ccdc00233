import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import { ETranslation } from '../constants/ETranslation.js';
import * as UserService from '../services/userService.js';

// Validates if user has the necessary permission
// Receives an array of functionalities. If user has any of the permissions he is allowed to call the route
// The functionalities array can also include roles and subroles
export const validatePermissions = (functionalities) => async (req, res, next) => {
  const authUserId = getStore().get(EAls.USER_ID);
  const permissions = await UserService.getAllPermissionsByUserId(authUserId);
  const hasPermission = functionalities.some((func) => permissions.some((perm) => perm.code === func.code
      && perm.permissions?.some((p) => p.action === func.action)));

  if (hasPermission) {
    return next();
  }

  return res.status(403).json({ error: ETranslation.USER_HAS_NO_PERMISSIONS });
};
