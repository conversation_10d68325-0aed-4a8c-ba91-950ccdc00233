import * as DeviceCookieService from '../services/deviceCookieService.js';

export const deviceCookieMiddleware = () => async (req, res, next) => {
  const isDeviceCookieValid = await DeviceCookieService.isValidDeviceCookie(req);
  const isDeviceLocked = await DeviceCookieService.isDeviceLocked(req);

  if (isDeviceCookieValid && isDeviceLocked) {
    return res.status(403).json();
  }

  return next();
};
