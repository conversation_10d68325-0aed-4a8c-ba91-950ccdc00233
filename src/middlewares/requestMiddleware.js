import crypto from 'crypto';
import { asyncLocalStorage, getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';

export const requestMiddleware = async (req, res, next) => {
  const store = new Map();
  const trackingId = crypto.randomUUID();

  store.set(EAls.TRACKING_ID, trackingId);
  store.set(EAls.ENDPOINT, req.originalUrl);
  req.trackingId = trackingId;

  asyncLocalStorage.run(store, async () => {
    await next();
  });
};

export const getTrackingId = () => getStore().get(EAls.TRACKING_ID) || 'N/A';

export const getEndPoint = () => getStore().get(EAls.ENDPOINT) || 'N/A';

export const runWithTracking = async (fn) => {
  const store = new Map();
  store.set(EAls.TRACKING_ID, crypto.randomUUID());

  await asyncLocalStorage.run(store, fn);
};
