import RequestException from '../exceptions/requestException.js';
import { log } from '../logging/logger.js';
import { getTrackingId } from './requestMiddleware.js';
import { ETranslation } from '../constants/ETranslation.js';

// eslint-disable-next-line no-unused-vars
export const errorMiddleware = async (err, req, res, next) => {
  if (err instanceof RequestException) {
    log('warn', err.message);
    return res.status(err.statusCode).json({ error: err.message });
  }

  log('error', err.message);
  log('error', err.stack);
  return res.status(500).json({
    error: `${ETranslation.UNEXPECTED_ERROR} #${getTrackingId()}`,
  });
};
