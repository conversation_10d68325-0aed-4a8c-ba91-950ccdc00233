import jwt from 'jsonwebtoken';
import { ETokenType } from '../constants/ETokenType.js';
import { getAllPermissionsByUserId } from '../services/userService.js';
import { getTokenFromRequest, getUserIdFromRequest } from '../utils/jwtTokenUtil.js';
import { log } from '../logging/logger.js';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';
import * as UserRepository from '../database/repositories/userRepository.js';
import { EUserState } from '../constants/EUserState.js';
import { ETranslation } from '../constants/ETranslation.js';

export const validateAuthentication = (requiredType) => async (req, res, next) => {
  try {
    const token = await getTokenFromRequest(req);
    if (!token) {
      log('warn', 'Authentication failed: Token is missing');
      return res.status(401).json({ error: ETranslation.MISSING_TOKEN });
    }

    let decoded;
    try {
      decoded = jwt.verify(
        token,
        requiredType === ETokenType.AUTH_REFRESH
          ? process.env.JWT_REFRESH_KEY
          : process.env.JWT_SECRET_KEY,
      );
    } catch (error) {
      log('warn', `Authentication failed: ${error.message}`);
      return res.status(401).json({ error: error.name === 'TokenExpiredError' ? 'JWT expired' : 'Invalid token' });
    }

    if (decoded.type !== requiredType) {
      log('warn', `Authentication failed: Invalid token type. Expected ${requiredType}`);
      return res.status(403).json({ error: `Invalid token type. Expected ${requiredType}` });
    }

    const user = await UserRepository.findById(decoded.userId);
    if (!user) {
      log('warn', 'User by id not found.');
      return res.status(401).json({ error: 'Invalid authentication' });
    }
    if (user.user_state === EUserState.BLOCKED) {
      log('warn', 'User is BLOCKED.');
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    if ((requiredType === ETokenType.AUTH_FULL && decoded.version !== user.access_token_version.toString())
                || (requiredType === ETokenType.AUTH_REFRESH && decoded.version !== user.refresh_token_version.toString())
    ) {
      log('warn', 'Token version unmatched.');
      return res.status(401).json({ error: 'Invalid authentication' });
    }

    const store = getStore();
    store.set(EAls.USER_ID, decoded.userId);

    return next();
  } catch (error) {
    log('error', `Unexpected Authentication error: ${error.message}`);
    return next(error);
  }
};

export const validateAuthorization = (requiredRoles) => async (req, res, next) => {
  try {
    if (requiredRoles.length > 0) {
      const userId = await getUserIdFromRequest(req);
      const userRoles = await getAllPermissionsByUserId(userId);

      const hasRequiredRole = requiredRoles.some((role) => userRoles.includes(role));
      if (!hasRequiredRole) {
        log('warn', `Authorization failed: User ${userId} does not have required role(s)`);
        return res.status(403).json({ error: 'Insufficient permissions' });
      }
    }

    return next();
  } catch (error) {
    log('error', `Unexpected Authorization error: ${error.message}`);
    return next(error);
  }
};

// Used for routes with optional authentication
// If token exists and is valid set the user id in store
// If the token does not exist or is invalid it does not return an error and does not set user in store
export const optionalAuthentication = () => async (req, res, next) => {
  try {
    const token = await getTokenFromRequest(req);
    if (!token) {
      return next();
    }

    let decoded;
    try {
      decoded = jwt.verify(
        token,
        process.env.JWT_SECRET_KEY,
      );
    } catch (error) {
      return next();
    }

    const user = await UserRepository.findById(decoded.userId);
    if (!user) {
      return next();
    }
    if (user.user_state === EUserState.BLOCKED) {
      return next();
    }

    if (decoded.version !== user.access_token_version.toString() || decoded.version !== user.refresh_token_version.toString()) {
      return next();
    }

    const store = getStore();
    store.set(EAls.USER_ID, decoded.userId);

    return next();
  } catch (error) {
    log('error', `Unexpected Authentication error: ${error.message}`);
    return next(error);
  }
};
