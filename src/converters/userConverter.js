export const convertUserToDTO = (user) => ({
  id: user.id,
  email: user.email,
  name: user.name,
  phone: user.phone,
  address: user.address,
  nif: user.nif,
  state: user.user_state,
  lastLogin: user.last_session_date,

  subRoles: user.subRoles?.map((subRole) => ({
    id: subRole.id,
    code: subRole.code,
    name: subRole.name,
    role: subRole.role
      ? {
        id: subRole.role.id,
        code: subRole.role.code,
        name: subRole.role.name, // Corrigido para mostrar o nome do role
      }
      : null,
  })) || [],

  teams: user.teams?.map((team) => ({
    id: team.id,
    name: team.name,
    branch_id: team.branch_id,
  })) || [],
});
export const convertUsersToDTO = (result) => ({
  users: result.rows.map(convertUserToDTO),
  total: result.count,
});
