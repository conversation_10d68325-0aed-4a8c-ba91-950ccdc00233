import { EFunctionalityModule } from '../constants/EFunctionalityModule.js';
import { ERole } from '../constants/ERole.js';

export const groupPermissionsBySubRoleAndModule = (permissions) => {
  const allModules = Object.values(EFunctionalityModule);
  const grouped = Object.values(
    permissions.reduce((acc, item) => {
      const subroleId = item.subrole_id;
      const roleCode = item.subRole.role.code;
      const subroleCode = item.subRole.code;
      const moduleName = item.functionality.module;

      // Initialize the subrole entry if not present
      if (!acc[subroleId]) {
        acc[subroleId] = {
          roleCode,
          subroleId,
          subroleCode,
          modules: Object.fromEntries(allModules.map((mod) => [mod, false])),
        };
      }

      // Mark the current module as true
      acc[subroleId].modules[moduleName] = true;
      return acc;
    }, {}),
  ).map(({
    roleCode, subroleId, subroleCode, modules,
  }) => ({
    roleCode,
    subroleId,
    subroleCode,
    modules,
  }));
  return grouped.sort((a, b) => {
    if (a.roleCode === ERole.SIBS && b.roleCode !== ERole.SIBS) return -1;
    if (a.roleCode !== ERole.SIBS && b.roleCode === ERole.SIBS) return 1;
    return a.roleCode.localeCompare(b.roleCode);
  });
};

export const formatSubRoleFunctionalities = (functionalities) => functionalities.map((f) => ({
  module: f.functionality.module,
  code: f.functionality.code,
  permissionAction: f.permission_action,
  permissionValue: f.permission_value,
}));
