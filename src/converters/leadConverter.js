import { log } from '../logging/logger.js';

const convertLeadToDTO = (
  lead,
) => {
  log('info', 'Entering method convertUserToDTO');
  return {
    id,
    status,
    name,
    email,
    contact,
    postal_code,
    location,
    district,
    county,
    nif,
    origin,
    message,
    userId,
    assignedDate,
    closingDate,
    closingReasonId,
    closingReason,
    creationDate,
    integration_gesmat_course,
    integration_gesmat_assinged_polo,
    integration_gesmat_formation_center,
    integration_gesmat_auth_rgpd_4,
    integration_gesmat_auth_rgpd_5,
    integration_gesmat_sent_gocontact,
    integration_gesmat_assigned_gocontact,
  };
};

const convertLeadsToDTO = (result) => {
  log('info', 'Entering method convertLeadsToDTO');
  return {
    users: result.map(convertLeadToDTO),
    total: result.count,
  };
};

export default {
  convertLeadToDTO,
  convertLeadsToDTO
}