export const ETranslation = Object.freeze({
  // BE CONTROLLED ERRORS
  PERMISSIONS_UPDATED_SUCCESSFULLY: 'permissions.updated.successfully',
  PERMISSIONS_UPDATE_ERROR: 'permissions.update.error',
  PERMISSIONS_DENIED: 'permissions.denied',
  UNEXPECTED_ERROR: 'unexpected.error',
  FILE_NOT_FOUND: 'file.not.found',
  REQUEST_BODY_INVALID: 'request.body.invalid',
  EMAIL_OR_PASSWORD_INCORRECT: 'email.or.password.incorrect',
  CATEGORY_IN_USE_BY_OTHER_APP: 'category.in.use.by.other.app',
  MESSAGE_NOT_SENT: 'message.not.sent',
  ROLE_NOT_FOUND: 'role.not.found',
  SUBROLE_NOT_FOUND: 'subrole.not.found',
  SUBROLE_ALREADY_EXISTS: 'subrole.already.exists',
  USER_ALREADY_EXISTS: 'user.already.exists',
  OTP_VALIDATION_FAILED: 'otp.validation.failed',
  USER_NOT_FOUND: 'user.not.found',
  ACCOUNT_VALIDATION_FAILED: 'account.validation.failed',
  LANGUAGE_NOT_FOUND: 'language.not.found',
  INVALID_NEW_PASSWORD: 'invalid.new.password',
  UNABLE_TO_REGISTER: 'unable.to.register',
  RESOURCE_ALREADY_EXISTS: 'category.already.exists',
  MULTIPLE_CSV_FILES_UPLOADED: 'multiple.csv.files.uploaded',
  MISSING_CSV_FILE: 'missing.csv.file',
  CONTENT_NOT_FOUND: 'content.not.found',
  USER_HAS_NO_PERMISSIONS: 'user.has.no.permissions',
  PASSWORD_INCORRECT: 'password.incorrect',
  MISSING_TOKEN: 'missing.token',
  MISSING_FILE: 'missing.file',
  MULTIPLE_FILES_UPLOADED: 'multiple.files.uploaded',

  // EMAIL
  EMAIL_SUBJECT_LOGIN_OTP: 'email.subject.login.otp',
  EMAIL_BODY_LOGIN_OTP: 'email.body.login.otp',
  EMAIL_SUBJECT_RECOVER_PASSWORD: 'email.subject.recover.password',
  EMAIL_BODY_RECOVER_PASSWORD_OTP: 'email.body.recover.password.otp',
  EMAIL_BODY_RECOVER_PASSWORD_TOKEN: 'email.body.recover.password.token',
  EMAIL_SUBJECT_ACCOUNT_VALIDATION: 'email.subject.account.validation',
  EMAIL_BODY_ACCOUNT_VALIDATION_OTP: 'email.body.account.validation.otp',
  EMAIL_BODY_ACCOUNT_VALIDATION_TOKEN: 'email.body.account.validation.token',
  EMAIL_SUBJECT_UPDATE_EMAIL_OTP: 'email.subject.update.email.otp',
  EMAIL_BODY_UPDATE_EMAIL_OTP: 'email.body.update.email.otp',
  EMAIL_BODY_UPDATE_EMAIL_TOKEN: 'email.body.update.email.token',

  // TEAMS:
  TEAM_ALREADY_EXISTS: 'team already exists',
  TEAM_DOES_NOT_EXIST: 'team does not exist',
  BRANCH_DOES_NOT_EXIST: 'branch does not exist',
  BRANCH_ALREADY_EXISTS: 'branch already exists',
});
