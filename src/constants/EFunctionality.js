import { EFunctionalityModule } from './EFunctionalityModule.js';

export const EFunctionality = Object.freeze({
  USER_MANAGEMENT: { code: 'USER_MANAGEMENT', module: EFunctionalityModule.MANAGEMENT },
  PERMISSION_MANAGEMENT: { code: 'PERMISSION_MANAGEMENT', module: EFunctionalityModule.MANAGEMENT },
  PASSWORD_RESET: { code: 'PASSWORD_RESET', module: EFunctionalityModule.MANAGEMENT },
  METRICS_VIEW: { code: 'METRICS_VIEW', module: EFunctionalityModule.METRICS },
  CHOOSE_LANGUAGE: { code: 'CHOOSE_LANGUAGE', module: EFunctionalityModule.MANAGEMENT },
  LANGUAGE_MANAGEMENT: { code: 'LANGUAGE_MANAGEMENT', module: EFunctionalityModule.MANAGEMENT },
});
