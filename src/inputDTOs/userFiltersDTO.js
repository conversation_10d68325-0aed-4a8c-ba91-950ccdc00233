import Joi from 'joi';
import { EUserState } from '../constants/EUserState.js';

const UserFiltersDTO = Joi.object({
  sortBy: Joi.string().valid('name', 'email', 'nif').default('email'),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),

  // required in order to get the correct list of users in role
  roleCode: Joi.string(),

  email: Joi.string().empty('').default(null),
  name: Joi.string().min(1).max(100).empty('')
    .default(null),
  state: Joi.string().valid(...Object.values(EUserState)).empty('').default(null),
  nif: Joi.string().max(100).empty('').default(null),
  subRoleCode: Joi.string().empty('').default(null),
});

export default UserFiltersDTO;
