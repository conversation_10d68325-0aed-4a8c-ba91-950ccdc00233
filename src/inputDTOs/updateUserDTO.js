import Joi from 'joi';
import { EUserState } from '../constants/EUserState.js';
import { PASSWORD_REGEX } from '../utils/passwordUtil.js';

const UpdateUserDTO = Joi.object({
  user_id: Joi.string().uuid({ version: 'uuidv4' }),
  name: Joi.string().min(1).max(1000).empty('')
    .default(null),
  nif: Joi.string().max(100).empty('').default(null),
  email: Joi.string().email().lowercase(),
  phone: Joi.string().max(20).empty('').default(null),
  entityId: Joi.string().uuid({ version: 'uuidv4' }),
  state: Joi.string().valid(...Object.values(EUserState)).empty('').default(null),
  subRoleCode: Joi.string().empty('').default(null),
  roleCode: Joi.string().empty('').default(null),
  newPassword: Joi.string().pattern(new RegExp(PASSWORD_REGEX)),
});

export default UpdateUserDTO;
