import Jo<PERSON> from 'joi';
import { PASSWORD_REGEX } from '../utils/passwordUtil.js';

const UpdateOwnUserDTO = Joi.object({
  user_id: Joi.string().uuid({ version: 'uuidv4' }).required(),
  name: Joi.string().min(1).max(1000).empty('')
    .default(null),
  nif: Joi.string().max(100).empty('').default(null),
  email: Joi.string().email().lowercase(),
  phone: Joi.string().max(20).empty('').default(null),
  currentPassword: Joi.string().pattern(new RegExp(PASSWORD_REGEX)),
  newPassword: Joi.string().pattern(new RegExp(PASSWORD_REGEX)),
  languageCode: Joi.string().max(6).empty('').default(null),
});

export default UpdateOwnUserDTO;
