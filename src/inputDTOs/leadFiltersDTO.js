import Joi from 'joi';

const LeadFiltersDTO = Joi.object({
  sortBy: Joi.string().valid('name', 'email', 'nif', 'location', 'district', 'county').default('email'),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),

  email: Joi.string().empty('').default(null),
  name: Joi.string().min(1).max(250).empty('').default(null),
  nif: Joi.string().max(20).empty('').default(null),
  location: Joi.string().empty('').default(null),
  district: Joi.number().default(null),
  county: Joi.number().default(null),
  status: Joi.string().empty('').default(null),
});

export default LeadFiltersDTO;
