import Joi from 'joi';
import { EFunctionality } from '../constants/EFunctionality.js';
import { EPermissionAction } from '../constants/EPermissionAction.js';
import { EPermissionValue } from '../constants/EPermissionValue.js';

const AddSubRoleDTO = Joi.object({
  code: Joi.string().min(1).max(1000).required(),
  roleId: Joi.string().uuid({ version: 'uuidv4' }).required(),
  permissions: Joi.array().items(Joi.object(({
    code: Joi.string().valid(...Object.values(EFunctionality).map((f) => f.code)),
    permissionAction: Joi.string().valid(...Object.values(EPermissionAction)),
    permissionValue: Joi.string().valid(...Object.values(EPermissionValue)),
  }))),
});

export default AddSubRoleDTO;
