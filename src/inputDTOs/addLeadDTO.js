import Joi from 'joi';

const AddLeadDTO = Joi.object({
  status: Joi.string().min(1).max(25).required(),
  name: Joi.string().min(1).max(250).required(),
  email: Joi.string().email().lowercase().required(),
  contact: Joi.string().max(20).empty('').default(null),
  postal_code: Joi.string().max(20).required(),
  location: Joi.string().max(100).required(),
  district: Joi.number().max(11).required(),
  county: Joi.number().max(11).required(),
  roleCode: Joi.string().required(),
  nif: Joi.string().max(20).empty('').default(null),
  origin: Joi.string().max(200).empty('').default(null),
  message: Joi.string().max(2500).empty('').default(null),
  userId: Joi.number().required(),
  assignedDate: Joi.date(),
  closingDate: Joi.date(),
  closingReasonId: Joi.number(),
  closingReason: Joi.string().max(20),
  creationDate: Joi.date(),
  integration_gesmat_course: Joi.integer(),
  integration_gesmat_assinged_polo: Joi.integer(),
  integration_gesmat_formation_center: Joi.integer(),
  integration_gesmat_auth_rgpd_4: Joi.boolean(),
  integration_gesmat_auth_rgpd_5: Joi.boolean(),
  integration_gesmat_sent_gocontact: Joi.boolean(),
  integration_gesmat_assigned_gocontact: Joi.boolean(),
});

export default AddLeadDTO;
