import Jo<PERSON> from 'joi';
import { PASSWORD_REGEX } from '../utils/passwordUtil.js';

const RegisterDTO = Joi.object({
  email: Joi.string().email().lowercase().required(),
  phone: Joi.string().max(20).empty('').default(null),
  name: Joi.string().min(1).max(1000).required(),
  password: Joi.string()
    .pattern(new RegExp(PASSWORD_REGEX))
    .required(),
  address: Joi.string().max(200).empty('').default(null),
  nif: Joi.string().max(20).empty('').default(null),
});

export default RegisterDTO;
