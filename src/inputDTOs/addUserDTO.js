import Joi from 'joi';

const AddUserDTO = Joi.object({
  name: Joi.string().min(1).max(1000).required(),
  email: Joi.string().email().lowercase().required(),
  phone: Joi.string().max(20).empty('').default(null),
  subRoleCode: Joi.string().required(),
  roleCode: Joi.string().required(),
  address: Joi.string().max(200).empty('').default(null),
  nif: Joi.string().max(20).empty('').default(null),
});

export default AddUserDTO;
