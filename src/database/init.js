import { sequelize } from './database.js';
import { log } from '../logging/logger.js';
import { setupAssociations } from './associations.js';

export const initDatabase = async () => {
  setupAssociations();

  try {
    await sequelize.authenticate();
    log('info', 'Database connection has been established successfully.');

    await sequelize.sync({ alter: true }); // or { force: true } in dev
  } catch (error) {
    log('error', `Unable to connect to the database: ${error}`);
    throw error;
  }
};
