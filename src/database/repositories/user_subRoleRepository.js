import { UserSubRole } from '../models/userSubRole.js';

export const addSubRoleOfUser = async (userId, subRoleId) => UserSubRole.create({
  user_id: userId,
  subrole_id: subRoleId,
});

export const deleteSubRolesOfUser = async (userId, subRoleIds) => UserSubRole.destroy({
  where: {
    user_id: userId,
    subrole_id: subRoleIds,
  },
});

export const findSubRoleIdsByUserId = async (userId) => UserSubRole.findOne({
  where: {
    user_id: userId,
  },
  attributes: ['subrole_id'],
});
