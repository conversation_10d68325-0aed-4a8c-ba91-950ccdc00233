import { UserTeam } from '../models/userTeam.js';
import { User } from '../models/user.js';
import { Team } from '../models/team.js';

export const associateUserToTeam = async (teamId, userId) => UserTeam.create({
  team_id: teamId,
  user_id: userId,
});

export const findTeamIdsfromUserId = async (userId) => UserTeam.findAll({
  where: {
    user_id: userId,
  },
  raw: true,
  attributes: ['team_id'],

});

export const findUsersByTeamId = async (teamId) => User.findAndCountAll({
  include: [{
    model: Team,
    as: 'teams',
    where: { id: teamId },
    attributes: [], // Não traz colunas do Team, só filtra
    through: { attributes: [] }, // Não traz colunas da tabela pivô UserTeam
  }],
  attributes: [
    'id',
    'email',
    'name',
    'phone',
    'address',
    'nif',
    'user_state',
    'last_session_date',
  ],
});

export const deleteUserFromTeam = async (params) => UserTeam.destroy({
  where: {
    user_id: params.user_id,
    team_id: params.team_id,
  },
});
