import { log } from '../../logging/logger.js';
import { Language } from '../models/language.js';

export const getLanguageByCode = async (languageCode) => Language.findOne({
  where: { code: languageCode },
});

export const addLanguage = async (languageCode) => {
  const language = await getLanguageByCode(languageCode);

  if (language) {
    log('warn', `Language ${languageCode} already exists`);
    return language;
  }

  return Language.create({
    code: languageCode,
  });
};

export const getAvailableLanguages = async () => Language.findAll();

export const getLanguageById = async (languageId) => Language.findOne({
  where: { id: languageId },
});

export const getActiveLanguages = async () => Language.findAll({ });
