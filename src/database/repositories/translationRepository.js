import { Op } from 'sequelize';
import { Translation } from '../models/translation.js';
import { Language } from '../models/language.js';
import * as LanguageRepository from './languageRepository.js';
import RequestException from '../../exceptions/requestException.js';
import { DEFAULT_LANGUAGE } from '../../constants/ELanguage.js';
import { sequelize } from '../database.js';
import { ETranslation } from '../../constants/ETranslation.js';
import { EDatabaseType } from '../../constants/EDatabaseType.js';

export const getTranslationsByLanguageCode = async (languageCode) => Translation.findAll({
  where: {
    translated_text: {
      [Op.notLike]: '<!DOCTYPE html>%',
    },
  },
  include: [{
    model: Language,
    as: 'language',
    required: true,
    where: { code: languageCode },
  }],
});

export const getTranslationByCodeAndLanguageCode = async (key, languageCode = DEFAULT_LANGUAGE) => {
  const language = await LanguageRepository.getLanguageByCode(languageCode);
  let code = languageCode;
  if (!language) {
    code = DEFAULT_LANGUAGE;
  }

  return Translation.findOne({
    where: { code: key },
    include: [{
      model: Language,
      as: 'language',
      required: true,
      where: { code },
    }],
  });
};

export const updateTranslation = async (translation) => translation.save();

export const addTranslation = async (languageCode, key, value) => {
  const language = await LanguageRepository.getLanguageByCode(languageCode);
  if (!language) {
    throw new RequestException(ETranslation.LANGUAGE_NOT_FOUND);
  }

  const translation = await getTranslationByCodeAndLanguageCode(key, languageCode);
  if (translation) {
    translation.translated_text = value;
    return updateTranslation(translation);
  }

  return Translation.create({
    code: key,
    translated_text: value,
    language_id: language.id,
  });
};

export const updateTranslationsBulk = async (languageId, updates) => {
  if (updates.length === 0) return;

  let sql;

  // The SQL query is different in MySQL and POSTGRES
  switch (process.env.BD_TYPE) {
    case EDatabaseType.MYSQL: {
      const union = updates
        .map((t) => `SELECT '${t.code.replace(/'/g, "''")}' AS code, '${t.translated_text.replace(/'/g, "''")}' AS translated_text`)
        .join('\nUNION ALL\n');

      sql = `
      UPDATE translations AS t
      JOIN (
        ${union}
      ) AS v ON t.code = v.code AND t.language_id = '${languageId}'
      SET t.translated_text = v.translated_text;
    `;
    }
      break;
    case EDatabaseType.POSTGRES: {
      const values = updates
        .map((t) => `('${t.code.replace(/'/g, "''")}', '${t.translated_text.replace(/'/g, "''")}')`)
        .join(',');

      sql = `
      UPDATE translations AS t
      SET translated_text = v.translated_text
      FROM (VALUES ${values}) AS v(code, translated_text)
      WHERE t.code = v.code AND t.language_id = '${languageId}';
    `;
    }
      break;
    default:
      throw new Error('Database type not configured');
  }
  await sequelize.query(sql);
};

export const getDistinctTranslationCodes = async () => Translation.findAll({
  attributes: ['code'],
  group: ['code'],
  raw: true,
});
