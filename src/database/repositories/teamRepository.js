import { Branch } from '../models/branch.js';
import { Team } from '../models/team.js';

export const findBranchById = async (id) => Branch.findOne({ where: { id } });

export const findBranchByName = async (name) => Branch.findOne({ where: { name } });

export const createBranch = async (name) => Branch.create({ name });

// TEAM

export const findTeamByName = async (name) => Team.findOne({
  where: { name },
  include: [{
    model: Branch,
    as: 'branch',
    required: false,
  }],
});

export const findTeamById = async (id) => Team.findOne({
  where: { id },
  include: [{
    model: Branch,
    as: 'branch',
    required: false,
  }],
});

export const createTeam = async (name, branchId) => Team.create({ name, branch_id: branchId });

export const getAllBranchesFiltered = async (params) => {
  const whereClause = {};
  if (params.id) {
    whereClause.id = params.id;
  }
  if (params.name) {
    whereClause.name = params.name;
  }
  const branches = await Branch.findAll({
    where: whereClause,
    attributes: ['id', 'name'],
  });
  return branches;
};

export const getAllTeamsFiltered = async (params) => {
  const whereClause = {};
  if (params.id) {
    whereClause.id = params.id;
  }
  if (params.name) {
    whereClause.name = params.name;
  }
  if (params.branch_id) {
    whereClause.branch_id = params.branch_id;
  }
  const result = await Team.findAll({
    where: whereClause,
    include: [{
      model: Branch,
      as: 'branch',
      required: false,
    }],
  });
  return result;
};

export const updateBranch = async (branch) => branch.save();

export const updateTeam = async (team) => team.save();
