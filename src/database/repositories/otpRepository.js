import { Op } from 'sequelize';
import { Otp } from '../models/otp.js';

export const findByUserIdAndType = async (userId, otpType) => Otp.findOne({
  where: {
    user_id: userId,
    type: otpType,
  },
});

export const findByUserIdAndTypeAndOtp = async (userId, otpType, otp) => Otp.findOne({
  where: {
    user_id: userId,
    type: otpType,
    otp,
  },
});

export const deleteOtp = async (userId, otpType) => Otp.destroy({
  where: {
    user_id: userId,
    type: otpType,
  },
});

export const createOtp = async (userId, otpType, otp, expiresAt, newField = null) => Otp.create({
  user_id: userId,
  type: otpType,
  otp,
  expires_at: expiresAt,
  newField,
});

export const deleteExpiredOtps = async () => Otp.destroy({
  where: {
    expires_at: {
      [Op.lt]: new Date(),
    },
  },
});
