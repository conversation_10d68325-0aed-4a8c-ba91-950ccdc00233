import * as Sequelize from 'sequelize';
import { Op } from 'sequelize';
import { User } from '../models/user.js';
import { sequelize } from '../database.js';
import { getStore } from '../../utils/asyncLocalStorage.js';
import { EAls } from '../../constants/EAls.js';
import { EUserState } from '../../constants/EUserState.js';
import * as LanguageRepository from './languageRepository.js';
import { DEFAULT_LANGUAGE } from '../../constants/ELanguage.js';
import { SubRole } from '../models/subRole.js';
import { Role } from '../models/role.js';
import { Team } from '../models/team.js';

const DEFAULT_USER_LIMT = 100;

export const findById = async (id) => User.findOne({
  where: { id },
  include: [{
    model: SubRole,
    as: 'subRoles',
    required: true,
    include: [{
      model: Role,
      as: 'role',
      required: true,
    }],
  }],
});

export const findByIdAndRoleCode = async (userId, roleCode) => {
  const query = `
        SELECT DISTINCT u.*
        FROM "user" u
            JOIN user_subrole usr ON usr.user_id = u.id
            JOIN subrole sr ON sr.id = usr.subrole_id
            JOIN role r ON r.id = sr.role_id
        WHERE u.id = :userId
            AND r.code = :roleCode
    `;

  const result = await sequelize.query(query, {
    replacements: {
      userId,
      roleCode,
    },
    type: sequelize.QueryTypes.SELECT,
    model: User,
    mapToModel: true,
  });

  return result[0];
};

export const findByEmail = async (email) => User.findOne({ where: { email } });

export const createUser = async (email, name, phone, hashedPassword, address, nif, languageCode, state = EUserState.PENDING_VALIDATION) => {
  const userId = getStore().get(EAls.USER_ID);
  let languageId;
  if (userId) {
    const user = await findById(userId);
    languageId = (await user.getSelectedLanguage()).id;
  } else {
    languageId = (await LanguageRepository.getLanguageByCode(
      languageCode || DEFAULT_LANGUAGE,
    )).id;
  }

  return User.create({
    email,
    phone,
    name,
    password_hash: hashedPassword,
    user_state: state,
    selected_language_id: languageId,
    address,
    nif,
  });
};

export const deleteUserById = async (id) => User.destroy({ where: { id } });

export const countUsersByRole = async () => {
  const result = await User.findAll({
    attributes: [
      [Sequelize.col('subroles.role.code'), 'roleCode'],
      [Sequelize.fn('COUNT', Sequelize.col('User.id')), 'userCount'],
    ],
    include: [{
      model: SubRole,
      as: 'subRoles',
      required: true,
      include: [{
        model: Role,
        as: 'role',
        required: true,
      }],
    }],
    group: ['subroles.role.code'],
    raw: true,
  });
  return result;
};

export const getAllFiltered = async (params) => {
  const whereClause = {};
  if (params.id) whereClause.id = params.id;
  if (params.email) whereClause.email = params.email;
  if (params.name) whereClause.name = params.name;
  if (params.state) whereClause.state = params.state;

  const subRoleWhere = {};
  if (params.subRoleCode) subRoleWhere.code = params.subRoleCode;

  const roleWhere = {};
  if (params.roleCode) roleWhere.code = params.roleCode;
  let requireValue = false;
  const teamWhere = {};
  if (params.teams && params.teams.length > 0) {
    requireValue = true;
    teamWhere.id = { [Op.in]: params.teams };
  }

  const [rows, count] = await Promise.all([
    User.findAll({
      where: whereClause,
      attributes: ['id', 'name', 'email', 'phone', 'address', 'nif', 'user_state'],
      offset: params.offset || 0,
      limit: params.limit || DEFAULT_USER_LIMT,
      order: [[params.sortBy || 'name', params.sortOrder || 'ASC']],
      include: [
        {
          model: SubRole,
          as: 'subRoles',
          required: false,
          where: subRoleWhere,
          include: [
            {
              model: Role,
              as: 'role',
              required: true,
              where: roleWhere,
            },
          ],
        },
        {
          model: Team,
          as: 'teams',
          required: requireValue,
          where: teamWhere,
          through: { attributes: [] },
        },
      ],
    }),
    User.count({
      where: whereClause,
      include: [
        {
          model: SubRole,
          as: 'subRoles',
          required: true,
          where: subRoleWhere,
          include: [
            {
              model: Role,
              as: 'role',
              required: true,
              where: roleWhere,
            },
          ],
        },
        {
          model: Team,
          as: 'teams',
          required: requireValue,
          where: teamWhere,
          through: { attributes: [] },
        },
      ],
      distinct: true,
    })]);
  return { rows, count };
};

export const updateUser = async (user) => user.save();
