import { SubRoleFunctionality } from '../models/subRoleFunctionality.js';
import { SubRole } from '../models/subRole.js';
import { Role } from '../models/role.js';
import { Functionality } from '../models/functionality.js';

export const createSubRoleFunctionality = async (subRoleId, functionalityId, permissionAction, permissionValue) => SubRoleFunctionality.create(
  {
    subrole_id: subRoleId,
    functionality_id: functionalityId,
    permission_action: permissionAction,
    permission_value: permissionValue,
  },
);

export const findByFunctionalityIdAndSubRoleIdPermissions = async (
  functionalityId,
  subRoleId,
  permissionAction,
  permissionValue,
) => SubRoleFunctionality.findOne({
  where: {
    functionality_id: functionalityId,
    subrole_id: subRoleId,
    permission_action: permissionAction,
    permission_value: permissionValue,
  },
});

export const findAll = async () => SubRoleFunctionality.findAll({
  include: [{
    model: SubRole,
    as: 'subRole',
    required: true,
    include: [{
      model: Role,
      as: 'role',
      required: true,
    }],
  },
  {
    model: Functionality,
    as: 'functionality',
    required: true,
  }],
});

export const findAllPermissionsBySubrole = async (subroleId) => SubRoleFunctionality.findAll({
  where: { subrole_id: subroleId },
  include: [
    {
      model: Functionality,
      as: 'functionality',
      required: true,
    }],
});

export const deleteSubRoleFunctionality = async (subRoleId, functionalityId) => SubRoleFunctionality.destroy(
  {
    where: {
      subrole_id: subRoleId,
      functionality_id: functionalityId,
    },
  },
);

export const deleteAllSubroleFunctionalities = async (subRoleId) => SubRoleFunctionality.destroy(
  {
    where: {
      subrole_id: subRoleId,
    },
  },
);

export const getPermission = async (subRoleId, functionalityId, permissionAction) => SubRoleFunctionality.findOne(
  {
    where: {
      subrole_id: subRoleId,
      functionality_id: functionalityId,
      permission_action: permissionAction,
    },
    attributes: ['permission_value'],
  },
);
