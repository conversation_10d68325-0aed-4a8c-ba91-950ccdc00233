import { Lead } from '../models/lead.js';
import { log } from '../../logging/logger.js';
import { sequelize } from '../database.js';

const DEFAULT_LEAD_LIMT = 100;

const findById = async (id) => {
  return Lead.findOne({ where: { id } });
};

// TODO: might not be required
const findByUserIdAndEmail = async (userId, email) => {
  const query = `
        SELECT DISTINCT l.*
        FROM "lead" l
        WHERE l.userId = :userId
            AND r.email = :email
    `;

  const result = await sequelize.query(query, {
    replacements: {
      userId,
      email,
    },
    type: sequelize.QueryTypes.SELECT,
    model: Lead,
    mapToModel: true,
  });

  return result[0];
};

// TODO: might not be required
const findByNif = async (nif) => {
  return Lead.findOne({ where: { nif } });
};

// TODO: needs testing
const createLead = async (lead) => {
  return Lead.create(lead);
};

const deleteLeadById = async (id) => {
  return Lead.destroy({ where: { id } });
};

const getAllFiltered = async (limit, offset, sortBy, sortOrder, email=null, name=null, nif=null, location=null, district=null, county=null, status=null) => {
  const whereClause = {
    email,
    name,
    nif,
    location,
    district,
    county,
    status
  };

  const result = await Lead.findAndCountAll({
    attributes: ['id, name', 'email', 'nif'],
    where: {
      whereClause,
    },
    offset,
    limit: limit || DEFAULT_LEAD_LIMT,
    order: [[sortBy, sortOrder]],
  });

  return {
    count: result.total,
    rows: result.rows,
  };
};

const updateLead = async (lead) => {
  return lead.save();
};

export default {
  findById,
  findByUserIdAndEmail,
  findByNif,
  createLead,
  deleteLeadById,
  getAllFiltered,
  updateLead
}