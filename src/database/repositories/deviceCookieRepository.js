import { Op } from 'sequelize';
import { DeviceCookie } from '../models/deviceCookie.js';

export const findByCookie = async (cookie) => DeviceCookie.findOne({
  where: { cookie },
});

export const findByEmail = async (email) => DeviceCookie.findOne({
  where: { email },
});

export const createDeviceCookie = async (cookie, email) => DeviceCookie.create({
  login_attempts: 1,
  email,
  cookie,
});

export const updateDeviceCookie = async (deviceCookie) => deviceCookie.save();

export const deleteOldDeviceCookies = async (olderThan) => DeviceCookie.destroy({
  where: {
    last_updated_date: {
      [Op.lt]: olderThan,
    },
  },
});
