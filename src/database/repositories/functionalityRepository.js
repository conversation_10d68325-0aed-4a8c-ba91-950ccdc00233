import { Functionality } from '../models/functionality.js';
import { UserSubRole } from '../models/userSubRole.js';
import { SubRoleFunctionality } from '../models/subRoleFunctionality.js';
import { SubRole } from '../models/subRole.js';
import { sequelize } from '../database.js';

export const findByCode = async (code) => Functionality.findOne({
  where: {
    code,
  },
});

export const createFunctionality = async (code, module) => Functionality.create({ code, module });

export const findFunctionalitiesByUserId = async (userId) => Functionality.findAll({
  include: [{
    model: SubRole,
    as: 'subRoles',
    required: true,
    include: [{
      model: SubRoleFunctionality,
      as: 'subRoleFunctionalities',
      required: true,
    },
    {
      model: UserSubRole,
      as: 'userSubRoles',
      where: {
        user_id: userId,
      },
      attributes: [],
      required: true,
    }],
  }],
});

export const findFunctionalitiesPermissionsByUserId = async (userId) => {
  const query = `
      SELECT 
      f.id AS functionality_id,
      f.code AS functionality_code,
      f.module AS functionality_module,
      JSON_ARRAYAGG(
        JSON_OBJECT(
          'action', sf.permission_action,
          'value', sf.permission_value
        )
      ) AS permissions
    FROM user_subroles usr
    JOIN subroles sr ON sr.id = usr.subrole_id
    JOIN subrole_functionalities sf ON sf.subrole_id = sr.id
    JOIN functionalities f ON f.id = sf.functionality_id
    WHERE usr.user_id = :userId
    GROUP BY f.id, f.code, f.module;
      `;

  const result = await sequelize.query(query, {
    replacements: { userId },
    type: sequelize.QueryTypes.SELECT,
  });

  return result;
};
