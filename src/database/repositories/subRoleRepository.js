import { SubRole } from '../models/subRole.js';
import { UserSubRole } from '../models/userSubRole.js';
import { getStore } from '../../utils/asyncLocalStorage.js';
import { EAls } from '../../constants/EAls.js';
import { sequelize } from '../database.js';
import { Role } from '../models/role.js';

export const findBySubRoleCodeAndRoleId = async (subRoleCode, roleId) => SubRole.findOne({
  where: {
    code: subRoleCode,
    role_id: roleId,
  },
});

export const findByCode = async (subRoleCode) => SubRole.findAll({ where: { code: subRoleCode } });

export const createSubRole = async (subRoleCode, roleId) => SubRole.create({
  code: subRoleCode,
  role_id: roleId,
});

export const deleteSubRole = async (subRoleId) => SubRole.destroy({ where: { id: subRoleId } });

export const findSubRolesByUserId = async (userId) => SubRole.findAll({
  include: [
    {
      model: UserSubRole,
      as: 'userSubRoles',
      where: {
        user_id: userId,
      },
      attributes: [],
      required: true,
    },
  ],
});

export const findAllFiltered = async (roleCode) => {
  const userId = getStore().get(EAls.USER_ID);

  const query = `
        SELECT sr.*
        FROM subrole sr
            INNER JOIN user_management_permission ump
                ON sr.id = ump.target_subrole_id
                AND sr.role_id = ump.target_role_id

            INNER JOIN user_subrole usr
                ON usr.subrole_id = ump.managing_subrole_id
                AND usr.user_id = :userId

            INNER JOIN subrole managing_subrole
                ON managing_subrole.id = usr.subrole_id

            INNER JOIN role managing_role
                ON managing_role.id = managing_subrole.role_id
                AND managing_role.id = ump.managing_role_id

            ${roleCode ? 'INNER JOIN role r ON sr.role_id = r.id' : ''}

        WHERE 1=1
            ${roleCode ? 'AND r.code = :roleCode' : ''}
    `;

  const params = { userId };
  if (roleCode) {
    params.roleCode = String(roleCode);
  }

  return sequelize.query(query, {
    replacements: params,
    type: sequelize.QueryTypes.SELECT,
    model: SubRole,
    mapToModel: true,
  });
};

export const findById = async (subRoleId) => SubRole.findOne({ where: { id: subRoleId } });

export const getAllSubroles = async () => SubRole.findAll({
  include: [
    {
      model: Role,
      as: 'role',
    },
  ],
});
