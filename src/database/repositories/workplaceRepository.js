import { Workplace } from '../models/workplace.js';

export const getUserWorkplace = async (userId) => Workplace.findAll({ attributes: ['key', 'value'], where: { user_id: userId } });

export const deleteUserWorkplace = async (userId) => Workplace.destroy({ where: { user_id: userId } });

export const updateUserWorkplace = async (userId, data) => {
  const upsertPromises = data.map(async ({ key, value }) => {
    await Workplace.upsert({
      user_id: userId,
      key,
      value,
    });
  });

  return Promise.all(upsertPromises);
};

export const countUserOpenTabs = async (userId) => (await Workplace.findOne({
  attributes: ['value'],
  where: {
    user_id: userId,
    key: 'OpenTabs',
  },
}))?.value?.length;
