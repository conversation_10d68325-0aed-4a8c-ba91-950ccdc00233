import { File } from '../models/file.js';

export const findFileById = async (id) => File.findByPk(id);

export const findFileByHash = async (hash) => File.findOne({ where: { content_hash: hash } });

export const filterFiles = async (entity, limit, offset, sortBy, sortOrder) => {
  const whereClause = {};
  if (entity) {
    whereClause.associated_entity = entity;
  }
  return File.findAndCountAll({
    where: whereClause,
    limit,
    offset,
    order: [[sortBy, sortOrder]],
  });
};

export const deleteFileById = async (id) => File.destroy({ where: { id } });

export const createFile = async (filename, mimetype, size, path, hash, entity) => File.create({
  filename,
  mimetype,
  size,
  storage_path: path,
  content_hash: hash,
  associated_entity: entity,
});
