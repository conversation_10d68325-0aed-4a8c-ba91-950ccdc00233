import { Role } from '../models/role.js';
import { UserSubRole } from '../models/userSubRole.js';
import { SubRole } from '../models/subRole.js';

export const findById = async (id) => Role.findOne({ where: { id } });

export const findByCode = async (code) => Role.findOne({ where: { code } });

export const createRole = async (code) => Role.create({ code });

export const findRolesByUserId = async (userId) => Role.findAll({
  include: [{
    model: SubRole,
    as: 'subRoles',
    required: true,
    include: [{
      model: UserSubRole,
      as: 'userSubRoles',
      where: { userId },
      attributes: [],
      required: true,
    }],
  }],
});
