import { Sequelize } from 'sequelize';
import { EDatabaseType } from '../constants/EDatabaseType.js';

const dbType = process.env.BD_TYPE;

function createSequelizeInstance() {
  switch (dbType) {
    case EDatabaseType.MYSQL:
      return new Sequelize(
        process.env.DB_NAME,
        process.env.DB_USER,
        process.env.DB_PASSWORD,
        {
          dialect: 'mysql',
          host: process.env.DB_HOST,
          port: process.env.DB_PORT,
          logging: false,
          define: {
            charset: 'utf8',
            collate: 'utf8_bin',
          },
          pool: {
            max: 20,
            min: 0,
            idle: 10000,
            acquire: 60000,
            evict: 1000,
          },
        },
      );

    case EDatabaseType.POSTGRES:
      return new Sequelize(
        process.env.DB_NAME,
        process.env.DB_USER,
        process.env.DB_PASSWORD,
        {
          host: process.env.DB_HOST,
          port: process.env.DB_PORT,
          dialect: 'postgres',
          define: {
            freezeTableName: true,
          },
          logging: false,
          transactionType: 'IMMEDIATE',
          isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
          autocommit: false,
          pool: {
            max: 100,
            min: 0,
            idle: 10000,
            acquire: 60000,
            evict: 1000,
          },
        },
      );

    default:
      throw new Error('Database type not configured');
  }
}

const sequelize = createSequelizeInstance();

export { sequelize };
