import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';

export const ALLOWED_WORKPLACE_KEYS = ['OpenTabs'];

export const Workplace = sequelize.define(
  'workplace',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    key: {
      type: DataTypes.ENUM(Object.values(ALLOWED_WORKPLACE_KEYS)),
      allowNull: false,
    },
    value: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'key'], // Composite unique constraint
        name: 'unique_userid_key',
      },
      {
        fields: ['user_id'], // Simple index for faster queries
        name: 'idx_user_id',
      },
    ],
  },
);
