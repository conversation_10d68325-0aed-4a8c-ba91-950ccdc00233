import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { baseOptions } from '../baseModelConfig.js';

export const Language = sequelize.define('language', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  code: {
    type: DataTypes.STRING(8),
    allowNull: false,
  },
}, {
  ...baseOptions,
  indexes: [
    {
      name: 'language_code_unique_idx',
      unique: true,
      fields: ['code'],
    },
  ],
});
