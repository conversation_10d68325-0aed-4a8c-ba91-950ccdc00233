import { DataTypes } from "sequelize";
import { sequelize } from "../database";

export const Opportunity = sequelize.define("lead", {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING(250),
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  status: {
    type: DataTypes.STRING(25),
    allowNull: false,
  },
  contact: {
    type: DataTypes.STRING(20),
    allowNull: true,
  },
  entity: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Entity,
      key: "id",
    },
  },
});
