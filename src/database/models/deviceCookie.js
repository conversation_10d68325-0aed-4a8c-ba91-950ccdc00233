import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';

export const DeviceCookie = sequelize.define('device_cookie', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  login_attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  locked_by_attempts: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  cookie: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING(200),
    allowNull: false,
  },
  ...auditBaseFields,
}, {
  ...baseOptions,
  indexes: [
    {
      name: 'device_cookie_cookie_unique_idx',
      unique: true,
      fields: ['cookie'],
    },

    {
      name: 'device_cookie_email_unique_idx',
      unique: true,
      fields: ['email'],
    },
  ],
});
