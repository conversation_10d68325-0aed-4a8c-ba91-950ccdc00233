import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { baseOptions, auditBaseFields } from '../baseModelConfig.js';
import { Branch } from './branch.js';

export const Team = sequelize.define(
  'team',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: Branch,
        key: 'id',
      },
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
  },

);
