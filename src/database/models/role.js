import { DataTypes } from 'sequelize';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { sequelize } from '../database.js';

export const Role = sequelize.define(
  'role',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    code: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
    indexes: [
      {
        name: 'role_code_unique_idx',
        unique: true,
        fields: ['code'],
      },
    ],
  },
);
