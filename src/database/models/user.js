import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { EUserState } from '../../constants/EUserState.js';
import { Language } from './language.js';

export const User = sequelize.define('user', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  email: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      isEmail: true,
    },
  },
  name: {
    type: DataTypes.STRING(1000),
    allowNull: true,
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
  },
  address: {
    type: DataTypes.STRING(200),
    allowNull: true,
  },
  nif: {
    type: DataTypes.STRING(20),
    allowNull: true,
  },
  user_state: {
    type: DataTypes.STRING(250),
    allowNull: false,
    defaultValue: EUserState.ACTIVE,
  },
  last_session_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  password_hash: {
    type: DataTypes.STRING(1000),
    allowNull: true,
  },
  last_password_reset: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  is_backoffice_created: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  validation_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  selected_language_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Language,
      key: 'id',
    },
  },
  access_token_version: {
    type: DataTypes.BIGINT,
    allowNull: false,
    defaultValue: 1,
    get() {
      return BigInt(this.getDataValue('access_token_version'));
    },
  },
  refresh_token_version: {
    type: DataTypes.BIGINT,
    allowNull: false,
    defaultValue: 1,
    get() {
      return BigInt(this.getDataValue('refresh_token_version'));
    },
  },
  ...auditBaseFields,
}, {
  ...baseOptions,
  indexes: [
    {
      name: 'user_email_unique_idx',
      unique: true,
      fields: ['email'],
    },
  ],
});
