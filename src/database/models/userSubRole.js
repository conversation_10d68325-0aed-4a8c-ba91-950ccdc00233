import { DataTypes } from 'sequelize';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { sequelize } from '../database.js';
import { User } from './user.js';
import { SubRole } from './subRole.js';

export const UserSubRole = sequelize.define(
  'user_subrole',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    subrole_id: {
      type: DataTypes.UUID,
      references: {
        model: SubRole,
        key: 'id',
      },
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
  },
);
