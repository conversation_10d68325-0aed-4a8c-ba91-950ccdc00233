import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { baseOptions } from '../baseModelConfig.js';
import { EFunctionalityModule } from '../../constants/EFunctionalityModule.js';

export const Functionality = sequelize.define(
  'functionality',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    module: {
      type: DataTypes.ENUM(Object.values(EFunctionalityModule)),
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
  },
  {
    ...baseOptions,
    indexes: [
      {
        name: 'functionality_code_unique_idx',
        unique: true,
        fields: ['code'],
      },
    ],
  },
);
