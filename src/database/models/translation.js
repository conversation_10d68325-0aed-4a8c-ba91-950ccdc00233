import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { Language } from './language.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';

export const Translation = sequelize.define('translation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  language_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Language,
      key: 'id',
    },
  },
  code: {
    type: DataTypes.STRING(250),
    allowNull: false,
  },
  translated_text: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  ...auditBaseFields,
}, {
  ...baseOptions,
  indexes: [
    {
      name: 'translation_unique_idx',
      unique: true,
      fields: ['language_id', 'code'],
    },
  ],
});
