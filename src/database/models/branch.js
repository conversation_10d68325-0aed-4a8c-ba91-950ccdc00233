import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { baseOptions, auditBaseFields } from '../baseModelConfig.js';

export const Branch = sequelize.define(
  'branch',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
  },
);
