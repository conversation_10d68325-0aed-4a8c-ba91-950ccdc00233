import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { Role } from './role.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';

export const SubRole = sequelize.define('subrole', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  code: {
    type: DataTypes.STRING(100),
    allowNull: false,
  },
  role_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Role,
      key: 'id',
    },
  },
  ...auditBaseFields,
}, {
  ...baseOptions,
  indexes: [
    {
      name: 'subrole_unique_idx',
      unique: true,
      fields: ['role_id', 'code'],
    },
  ],
});
