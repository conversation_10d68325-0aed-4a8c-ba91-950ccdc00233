import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { baseOptions } from '../baseModelConfig.js';

export const Lead = sequelize.define(
  'lead',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    status: {
      type: DataTypes.STRING(25),
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(250),
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING(250),
      allowNull: false,
    },
    contact: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    postal_code: {
      type: DataTypes.STRING(20),
      allowNull: false,
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    district: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    county: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    nif: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    origin: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    assignedDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    closingDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    closingReasonId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    closingReason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    creationDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    integration_gesmat_course: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    integration_gesmat_assinged_polo: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    integration_gesmat_formation_center: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    integration_gesmat_auth_rgpd_4: {
      type: DataTypes.TINYINT,
      allowNull: true,
    },
    integration_gesmat_auth_rgpd_5: {
      type: DataTypes.TINYINT,
      allowNull: true,
    },
    integration_gesmat_sent_gocontact: {
      type: DataTypes.TINYINT,
      allowNull: true,
    },
    integration_gesmat_assigned_gocontact: {
      type: DataTypes.TINYINT,
      allowNull: true,
    },
  },
  {
    ...baseOptions,
    indexes: [
      {
        name: 'lead_code_unique_idx',
        unique: true,
        fields: ['id'],
      },
    ],
  },
);
