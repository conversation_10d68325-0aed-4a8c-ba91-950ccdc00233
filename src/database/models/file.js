import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js'; // adjust path

export const File = sequelize.define(
  'file',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    filename: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    mimetype: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    size: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    storage_path: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    content_hash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    // Used to associate the file with something
    associated_entity: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    indexes: [
      {
        fields: ['content_hash'],
        name: 'idx_content_hash',
      },
      {
        fields: ['associated_entity'],
        name: 'idx_associated_entity',
      },
    ],
  },
);
