import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { SubRole } from './subRole.js';
import { Functionality } from './functionality.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { EPermissionAction } from '../../constants/EPermissionAction.js';
import { EPermissionValue } from '../../constants/EPermissionValue.js';

export const SubRoleFunctionality = sequelize.define('subrole_functionality', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  subrole_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SubRole,
      key: 'id',
    },
  },
  functionality_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Functionality,
      key: 'id',
    },
  },
  permission_action: {
    type: DataTypes.ENUM(Object.values(EPermissionAction)),
    allowNull: true,
    unique: false,
  },
  permission_value: {
    type: DataTypes.ENUM(Object.values(EPermissionValue)),
    allowNull: true,
    unique: false,
  },
  ...auditBaseFields,
}, {
  ...baseOptions,
});
