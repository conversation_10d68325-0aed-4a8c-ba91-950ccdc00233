import { DataTypes } from 'sequelize';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { sequelize } from '../database.js';
import { User } from './user.js';
import { Team } from './team.js';

export const UserTeam = sequelize.define(
  'user_teams',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    team_id: {
      type: DataTypes.UUID,
      references: {
        model: Team,
        key: 'id',
      },
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
  },
);
