import { DataTypes } from 'sequelize';
import { sequelize } from '../database.js';
import { auditBaseFields, baseOptions } from '../baseModelConfig.js';
import { User } from './user.js';

export const Otp = sequelize.define(
  'otp',
  {
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    type: {
      type: DataTypes.STRING(200),
      primaryKey: true,
    },
    otp: { // Used to store OTP and tokens
      type: DataTypes.STRING(100),
      primaryKey: true,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    newField: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    ...auditBaseFields,
  },
  {
    ...baseOptions,
  },
);
