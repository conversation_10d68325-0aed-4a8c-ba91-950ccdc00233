import { log } from '../logging/logger.js';
import { User } from './models/user.js';
import { UserSubRole } from './models/userSubRole.js';
import { Translation } from './models/translation.js';
import { SubRole } from './models/subRole.js';
import { SubRoleFunctionality } from './models/subRoleFunctionality.js';
import { Role } from './models/role.js';
import { Language } from './models/language.js';
import { Functionality } from './models/functionality.js';
import { Otp } from './models/otp.js';
import { Workplace } from './models/workplace.js';
import { Team } from './models/team.js';
import { Branch } from './models/branch.js';
import { UserTeam } from './models/userTeam.js';
// Setup associations between tables in the database
export const setupAssociations = () => {
  // Role ↔ SubRole
  Role.hasMany(SubRole, { foreignKey: 'role_id', as: 'subRoles' });
  SubRole.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });

  SubRole.hasMany(SubRoleFunctionality, { foreignKey: 'subrole_id', as: 'subRoleFunctionalities' });
  Functionality.hasMany(SubRoleFunctionality, { foreignKey: 'functionality_id', as: 'functionalityPermissions' });

  SubRoleFunctionality.belongsTo(SubRole, { foreignKey: 'subrole_id' });

  // SubRole ↔ UserSubRole
  SubRole.hasMany(UserSubRole, { foreignKey: 'subrole_id', as: 'userSubRoles' });

  // SubRole ↔ Functionality
  Functionality.belongsToMany(SubRole, {
    through: {
      model: SubRoleFunctionality,
      unique: false,
    },
    foreignKey: 'functionality_id',
    otherKey: 'subrole_id',
    as: 'subRoles',
  });

  SubRole.belongsToMany(Functionality, {
    through: {
      model: SubRoleFunctionality,
      unique: false,
    },
    foreignKey: 'subrole_id',
    otherKey: 'functionality_id',
    as: 'functionalities',
  });

  // User ↔ SubRole
  User.belongsToMany(SubRole, {
    through: UserSubRole, foreignKey: 'user_id', otherKey: 'subrole_id', as: 'subRoles',
  });
  SubRole.belongsToMany(User, {
    through: UserSubRole, foreignKey: 'subrole_id', otherKey: 'userId', as: 'users',
  });

  // UserSubRole ↔ User & SubRole
  UserSubRole.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  UserSubRole.belongsTo(SubRole, { foreignKey: 'subrole_id', as: 'subRole' });
  User.hasMany(UserSubRole, { foreignKey: 'user_id', as: 'userSubRoles' });

  // SubRoleFunctionality ↔ SubRole & Functionality
  SubRoleFunctionality.belongsTo(SubRole, { foreignKey: 'subrole_id', as: 'subRole' });
  SubRoleFunctionality.belongsTo(Functionality, { foreignKey: 'functionality_id', as: 'functionality' });
  // Otp ↔ User
  Otp.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(Otp, { foreignKey: 'user_id', as: 'otps' });

  // Language ↔ Translation
  Language.hasMany(Translation, { foreignKey: 'language_id', as: 'translations' });
  Translation.belongsTo(Language, { foreignKey: 'language_id', as: 'language' });

  // User ↔ Language
  User.belongsTo(Language, { foreignKey: 'selected_language_id', as: 'selectedLanguage' });
  Language.hasMany(User, { foreignKey: 'selected_language_id', as: 'users' });

  // User ↔ Workplace
  Workplace.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(Workplace, { foreignKey: 'user_id', as: 'workplace' });

  // Team ↔ Branch
  // Role.hasMany(SubRole, { foreignKey: 'role_id', as: 'subRoles' });
  // SubRole.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
  Branch.hasMany(Team, { foreignKey: 'branch_id', as: 'teams' });
  Team.belongsTo(Branch, { foreignKey: 'branch_id', as: 'branch' });

  // User ↔ Team
  User.belongsToMany(Team, {
    through: UserTeam, foreignKey: 'user_id', otherKey: 'team_id', as: 'teams',
  });
  Team.belongsToMany(User, {
    through: UserTeam, foreignKey: 'team_id', otherKey: 'user_id', as: 'users',
  });
  UserTeam.belongsTo(User, { foreignKey: 'user_id', as: 'user' }); // <-- ADICIONAR ESTA LINHA

  log('info', 'Associations initialized.');
};
