import { DataTypes } from 'sequelize';
import { getStore } from '../utils/asyncLocalStorage.js';
import { EAls } from '../constants/EAls.js';

export const auditBaseFields = {
  created_by: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'BE_API', // Default for non-authenticated actions
  },
  created_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  last_updated_by: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  last_updated_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
};

export const inactiveFields = {
  is_inactive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  inactive_date: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  inactive_by: {
    type: DataTypes.UUID,
    allowNull: true,
  },
};

export const baseOptions = {
  timestamps: false,
  underscored: true,
  hooks: {
    beforeCreate: async (record) => {
      const userId = getStore().get(EAls.USER_ID);
      record.created_by = userId || 'BE_SYSTEM';
      record.created_date = new Date();
    },
    beforeUpdate: async (record) => {
      const userId = getStore().get(EAls.USER_ID);
      record.last_updated_by = userId || 'BE_SYSTEM';
      record.last_updated_date = new Date();
    },
  },
};
