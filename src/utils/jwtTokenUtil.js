import jwt from 'jsonwebtoken';
import * as UserRepository from '../database/repositories/userRepository.js';
import { ETokenType } from '../constants/ETokenType.js';
import RequestException from '../exceptions/requestException.js';
import { findRolesByUserId } from '../database/repositories/roleRepository.js';
import { findFunctionalitiesByUserId } from '../database/repositories/functionalityRepository.js';

export const createAuthToken = async (userId, isAuthFull = false) => {
  const user = await UserRepository.findById(userId);

  if (!user) {
    throw new RequestException('User not found');
  }

  if (!isAuthFull) {
    const partialToken = jwt.sign(
      {
        userId: user.id,
        type: ETokenType.AUTH_PARTIAL,
      },
      process.env.JWT_SECRET_KEY,
      {
        expiresIn: process.env.JWT_PARTIAL_EXPIRE,
      },
    );

    return { partialToken };
  }

  const roles = (await findRolesByUserId(userId)).flatMap((entry) => entry.subRoles.map((sub) => ({
    role: entry.code,
    subrole: sub.code,
  })));
  const functionalities = (await findFunctionalitiesByUserId(userId)).map((f) => f.code);

  // Generate Access Token
  const accessToken = jwt.sign(
    {
      userId: user.id,
      name: user.name,
      email: user.email,
      version: user.access_token_version.toString(),
      profiles: roles,
      functionalities,
      type: ETokenType.AUTH_FULL,
    },
    process.env.JWT_SECRET_KEY,
    {
      expiresIn: process.env.JWT_EXPIRE,
    },
  );

  // Generate Refresh Token
  const refreshToken = jwt.sign(
    {
      userId: user.id,
      version: user.refresh_token_version.toString(),
      type: ETokenType.AUTH_REFRESH,
    },
    process.env.JWT_REFRESH_KEY,
    {
      expiresIn: process.env.JWT_REFRESH_EXPIRE,
    },
  );

  return { accessToken, refreshToken };
};

export const getUserIdFromToken = (tokenDecoded) => tokenDecoded.userId;

export const getTokenFromRequest = async (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return null;
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return null;
  }

  return token;
};

export const getUserIdFromRequest = async (req) => {
  const token = await getTokenFromRequest(req);
  return token.userId;
};
