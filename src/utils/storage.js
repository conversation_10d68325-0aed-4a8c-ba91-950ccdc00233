import multer from 'multer';
import fs from 'fs/promises';
import path from 'path';
import { sanitizeFilename, ensureTempDir } from './fileUtils.js';
import { getTrackingId } from '../middlewares/requestMiddleware.js';
import { getStore, asyncLocalStorage } from './asyncLocalStorage.js';

ensureTempDir(); // Ensures folder ./temp is created

const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      const trackingId = getTrackingId() !== 'N/A' ? getTrackingId() : req.trackingId;
      const userTempDir = path.join('./temp', trackingId);
      await fs.mkdir(userTempDir, { recursive: true }); // async version
      cb(null, userTempDir);
    } catch (err) {
      cb(err);
    }
  },
  filename: (req, file, cb) => cb(null, sanitizeFilename(file.originalname)),
});

export const upload = multer({ storage });

export const multerWithContext = (uploadMiddleware) => (handler) => (req, res, next) => {
  const store = getStore();
  uploadMiddleware(req, res, (err) => {
    if (err) return next(err);
    return asyncLocalStorage.run(store, () => handler(req, res, next));
  });
};
