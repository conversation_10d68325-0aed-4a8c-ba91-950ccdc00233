import bcryptjs from 'bcryptjs';

export const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*()_+{}[\]:;<>,.?~\\/-]).{12,}$/;

export const hashPassword = async (clearPassword) => bcryptjs.hash(clearPassword, 12);

export const validatePasswordHash = async (clearPassword, hashedPassword) => bcryptjs.compare(clearPassword, hashedPassword);

export const isValidPassword = (password) => {
  if (!password) {
    return false;
  }

  return PASSWORD_REGEX.test(password);
};

const generateRandomPassword = () => {
  const length = 12;
  const lowerCaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const upperCaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numberChars = '0123456789';
  const specialChars = '!@#$%^&*()_+{}[]:;<>,.?~\\/-';
  const allChars = lowerCaseChars + upperCaseChars + numberChars + specialChars;

  let password = '';
  password += lowerCaseChars[Math.floor(Math.random() * lowerCaseChars.length)];
  password += upperCaseChars[Math.floor(Math.random() * upperCaseChars.length)];
  password += numberChars[Math.floor(Math.random() * numberChars.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  for (let i = password.length; i < length; i += 1) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  password = password.split('').sort(() => Math.random() - 0.5).join('');

  if (!isValidPassword(password)) {
    return generateRandomPassword();
  }

  return password;
};

export const generateRandomHashedPassword = async () => {
  const password = generateRandomPassword();
  const hashedPassword = await hashPassword(password);
  return hashedPassword;
};
