import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

export const generateHash = (localPath) => {
  const fileBuffer = fs.readFileSync(localPath);
  return crypto.createHash('sha256').update(fileBuffer).digest('hex');
};

export const sanitizeFilename = (name) => name.replace(/[^a-zA-Z0-9._-]/g, '_');

export const ensureTempDir = () => {
  const tempDir = path.resolve('temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir);
  }
};
