import winston from 'winston';
import path from 'path';
import { getTrackingId, getEndPoint } from '../middlewares/requestMiddleware.js';

function getCallerInfo() {
  const oldPrepareStackTrace = Error.prepareStackTrace;
  Error.prepareStackTrace = (_, stack) => stack;
  const err = new Error();
  const { stack } = err;
  Error.prepareStackTrace = oldPrepareStackTrace;

  const callerFrame = stack[2];
  const fileName = path.basename(callerFrame.getFileName());
  const lineNumber = callerFrame.getLineNumber();
  return { fileName, lineNumber };
}

const logger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({
      level, message, timestamp, file, line,
    }) => JSON.stringify({
      timestamp,
      level,
      file,
      line,
      trackingId: getTrackingId(),
      endpoint: getEndPoint(),
      message,
    })),
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
    new winston.transports.File({ filename: 'logs/app.log' }),
    new winston.transports.Stream({ stream: process.stderr }),
  ],
});

export const log = async (level, message) => {
  const { fileName, lineNumber } = getCallerInfo();
  logger.log({
    level, message, file: fileName, line: lineNumber,
  });
};

export default logger;
