import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import routes from './src/routes/routes.js';
import { errorMiddleware } from './src/middlewares/errorMiddleware.js';
import { loadData } from './src/dataLoaders/index.js';
import { requestMiddleware } from './src/middlewares/requestMiddleware.js';
import { log } from './src/logging/logger.js';
import { initDatabase } from './src/database/init.js';
import { setupSwagger } from './swagger/swagger.js';
import { EEnvironmentVariableValues } from './src/constants/EEnvironmentVariableValues.js';

const startServer = async () => {
  try {
    // Initialize database before starting Express
    await initDatabase();

    const app = express();

    app.use(cookieParser());
    app.use(requestMiddleware);

    app.use(cors());
    app.use(express.json({ limit: '50mb' }));
    app.use(express.urlencoded({ extended: true, limit: '50mb' }));
    app.use('/api', routes);
    setupSwagger(app);
    app.use(errorMiddleware);

    // Load default data on DB
    if (process.env.RUN_DATALOADER === EEnvironmentVariableValues.TRUE) {
      await loadData();
    }

    const port = process.env.BACKEND_PORT || 3000;
    app.listen(port, () => {
      log('info', `Backend listening on port ${port}`);
    });
  } catch (error) {
    log('error', `Server failed to start: ${error}`);
  }
};

startServer();
