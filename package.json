{"name": "be_nodejs", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": ">=22.16.0"}, "dependencies": {"@azure/communication-email": "^1.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "pg": "^8.16.3", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "globals": "^16.2.0", "nodemon": "^3.1.10"}}