# Introduction

# Prerequisites

- NodeJS >= v22.16
- npm >= 10.9.2

# Development

Install pacakges:

```
npm install
```

# Database instructions

Use this docker command to run the MySQL database container (it is recommended to change the MYSQL_ROOT_PASSWORD to a safer one):

```
sudo docker run --cap-add=sys_nice --detach --name=mysql --env="MYSQL_ROOT_PASSWORD=root_password" --publish 3306:3306 --log-driver=journald --restart unless-stopped mysql:8.0.42-debian
```

Connect to the database with this command:

```
mysql -h 127.0.0.1 -P 3306 -u root -p
```

Use the following SQL commands to create the database and the backend user (it is recommended to change the backend user password to a safer one)

```
CREATE DATABASE db;

CREATE USER 'backend'@'localhost' IDENTIFIED BY 'password';

GRANT ALL PRIVILEGES ON db.* TO 'backend'@'localhost';

ALTER USER 'backend'@'localhost' IDENTIFIED WITH mysql_native_password BY 'password';

CREATE USER 'backend'@'%' IDENTIFIED BY 'password';

GRANT ALL PRIVILEGES ON db.* TO 'backend'@'%';

ALTER USER 'backend'@'%' IDENTIFIED WITH mysql_native_password BY 'password';

FLUSH PRIVILEGES;
```

Place the backend user password in the .env in the DB_PASS field


# Run

Create and configure the environment in the .env file. (See needed variables in .env.template file.)

Start server in development mode:

```
npm run dev
```
