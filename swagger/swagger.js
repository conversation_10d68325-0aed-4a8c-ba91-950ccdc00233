/* eslint-disable no-underscore-dangle */
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// const options = {
//   definition: {
//     openapi: '3.0.0',
//     info: {
//       title: 'Documentação da API',
//       version: '1.0.0',
//       description: 'Documentação da API usando Swagger',
//     },

//   },
//   apis: [path.join(__dirname, '../src/routes/**/*.js')],
//   // apis: [path.join(__dirname, '../../apps/terminal/routes/**/*.js')],
// };

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Documentação da API',
      version: '1.0.0',
      description: 'Documentação da API usando Swagger',
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [path.join(__dirname, '../src/routes/**/*.js')],
};

const swaggerSpec = swaggerJsdoc(options);

export const setupSwagger = (app) => {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
};
