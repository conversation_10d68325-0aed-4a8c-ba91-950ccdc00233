{"info": {"_postman_id": "bb895e65-f196-40ad-b5b6-027ba2e03425", "name": "Node Backend template", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "19428126"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "", "if (response && response.partialToken){", "    pm.environment.set(\"partialToken\", response.partialToken);", "} else if (response && response.accessToken && response.refreshToken){", "    pm.environment.set(\"accessToken\", response.accessToken);", "    pm.environment.set(\"refreshToken\", response.refreshToken);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{email}}\",\r\n    \"password\": \"{{password}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "Resend Login OTP", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{partialToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/auth/resend-login-otp", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "resend-login-otp"]}}, "response": []}, {"name": "Validate Login OTP", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "", "if (response){", "    pm.environment.set(\"accessToken\", response.accessToken);", "    pm.environment.set(\"refreshToken\", response.refreshToken);", "}", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{partialToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"otp\": \"966033\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/validate-otp", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "validate-otp"]}}, "response": []}, {"name": "Ref<PERSON>", "event": [{"listen": "test", "script": {"exec": ["let response = pm.response.json();", "", "if (response){", "    pm.environment.set(\"accessToken\", response.accessToken);", "    pm.environment.set(\"refreshToken\", response.refreshToken);", "}", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{refreshToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}}, "response": []}, {"name": "Send Recover password OTP/Token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/send-recover-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "send-recover-password"]}}, "response": []}, {"name": "Recover Password with OTP/Token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"newPassword\": \"************!Mm\",\r\n    \"otpOrToken\": \"eSHxANIMS9LUWPjgvoL8LyRzBNxyvCgh/qe3AFCFPumnuW/PbIrlyuGCCOzQ4RKjqbcscKOyBirO9iA+/JqeTg==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/recover-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "recover-password"]}}, "response": []}, {"name": "Register External User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{        \r\n    \"email\": \"<EMAIL>\",\r\n    \"phone\": \"*********\",\r\n    \"name\": \"<PERSON>\",\r\n    \"password\": \"************!Mm\",\r\n    \"address\": \"Morada falsa\",\r\n    \"nif\": \"516151\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/register?languageCode=pt", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register"], "query": [{"key": "languageCode", "value": "pt"}]}}, "response": []}, {"name": "Validate Account with OTP/Token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp\": \"6XDEgikvl7lAVLNbv/htacf8NBR01DfGkEx9pq01T6YFxM8o3uD0d4LnjS568fEyVfePHEPvpsJPmF20lA9qsQ==\",\r\n    \"password\": \"************!Mm\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/validate-account", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "validate-account"]}}, "response": []}, {"name": "Resend Validate Account OTP/Token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/auth/resend-validate-account-otp", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "resend-validate-account-otp"]}}, "response": []}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}}, "response": []}]}, {"name": "users", "item": [{"name": "Get Users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users/list?roleCode=EXTERNAL_USER", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "list"], "query": [{"key": "roleCode", "value": "EXTERNAL_USER"}]}}, "response": []}, {"name": "Get User by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users/4665529a-02d9-46c0-8b14-982eac4b5eba", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "4665529a-02d9-46c0-8b14-982eac4b5eba"]}}, "response": []}, {"name": "List user states", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users/user-states", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "user-states"]}}, "response": []}, {"name": "Update User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/users/4665529a-02d9-46c0-8b14-982eac4b5eba", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "4665529a-02d9-46c0-8b14-982eac4b5eba"]}}, "response": []}, {"name": "Update Own User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Francisco António2\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/users", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users"]}}, "response": []}, {"name": "Validate Email OTP", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"559445\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/users/email/validate-otp", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "email", "validate-otp"]}}, "response": []}, {"name": "Resend Update Email OTP/Token", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users/email/resend-otp?languageCode=pt", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "email", "resend-otp"], "query": [{"key": "languageCode", "value": "pt"}]}}, "response": []}, {"name": "Get Own User Info", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users"]}}, "response": []}, {"name": "Create User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"name\": \"Francisco <PERSON>ón<PERSON>\",\r\n    \"phone\": \"12345678\",\r\n    \"entityId\": \"cd7ec87f-ba5c-4057-a788-71a5e97eaef7\",\r\n    \"subRoleCode\": \"ADMIN\",\r\n    \"roleCode\": \"SIBS\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/users", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users"]}}, "response": []}]}, {"name": "Translations", "item": [{"name": "Get Translations By Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/translations/pt", "host": ["{{baseUrl}}"], "path": ["api", "v1", "translations", "pt"]}}, "response": []}, {"name": "Get Active Languages", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/translations/languages", "host": ["{{baseUrl}}"], "path": ["api", "v1", "translations", "languages"]}}, "response": []}, {"name": "Get Translations CSV By Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/translations/csv/pt", "host": ["{{baseUrl}}"], "path": ["api", "v1", "translations", "csv", "pt"]}}, "response": []}, {"name": "Get Translations CSV Template", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/translations/csv/template", "host": ["{{baseUrl}}"], "path": ["api", "v1", "translations", "csv", "template"]}}, "response": []}, {"name": "Update Translations CSV By Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/home/<USER>/Downloads/pt.csv"}]}, "url": {"raw": "{{baseUrl}}/api/v1/translations/csv/pt", "host": ["{{baseUrl}}"], "path": ["api", "v1", "translations", "csv", "pt"]}}, "response": []}]}, {"name": "roles", "item": [{"name": "get sub roles", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/subroles", "host": ["{{baseUrl}}"], "path": ["v1", "subroles"]}}, "response": []}]}, {"name": "Health", "item": [{"name": "Liveness", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/healthz", "host": ["{{baseUrl}}"], "path": ["v1", "healthz"]}}, "response": []}, {"name": "Readiness", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/readyz", "host": ["{{baseUrl}}"], "path": ["v1", "readyz"]}}, "response": []}]}, {"name": "Permissions", "item": [{"name": "Get modules by Sub Role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/permissions", "host": ["{{baseUrl}}"], "path": ["api", "v1", "permissions"]}}, "response": []}, {"name": "Get SubRole permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/permissions/e8aa2fa5-8c34-4c8e-88e9-2c1fceffa788", "host": ["{{baseUrl}}"], "path": ["api", "v1", "permissions", "e8aa2fa5-8c34-4c8e-88e9-2c1fceffa788"]}}, "response": []}, {"name": "Update SubRole permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "[\n    {\n        \"code\": \"CHOOSE_LANGUAGE\",\n        \"permissionAction\": \"UPDATE\",\n        \"permissionValue\": \"TEAM\"\n    },\n    {\n        \"code\": \"METRICS_VIEW\",\n        \"permissionAction\": \"CREATE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"PASSWORD_RESET\",\n        \"permissionAction\": \"UPDATE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"CHOOSE_LANGUAGE\",\n        \"permissionAction\": \"READ\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"PASSWORD_RESET\",\n        \"permissionAction\": \"DELETE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"CHOOSE_LANGUAGE\",\n        \"permissionAction\": \"CREATE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"METRICS_VIEW\",\n        \"permissionAction\": \"DELETE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"METRICS_VIEW\",\n        \"permissionAction\": \"READ\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"CHOOSE_LANGUAGE\",\n        \"permissionAction\": \"DELETE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"PASSWORD_RESET\",\n        \"permissionAction\": \"CREATE\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"PASSWORD_RESET\",\n        \"permissionAction\": \"READ\",\n        \"permissionValue\": \"ALL\"\n    },\n    {\n        \"code\": \"METRICS_VIEW\",\n        \"permissionAction\": \"UPDATE\",\n        \"permissionValue\": \"ALL\"\n    }\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/permissions/e8aa2fa5-8c34-4c8e-88e9-2c1fceffa788", "host": ["{{baseUrl}}"], "path": ["api", "v1", "permissions", "e8aa2fa5-8c34-4c8e-88e9-2c1fceffa788"]}}, "response": []}]}, {"name": "Workplaces", "item": [{"name": "Get User Workplace", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/workplaces", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workplaces"]}}, "response": []}, {"name": "Update User Workplace", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"OpenTabs\": [\"new values\", \"test \"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workplaces", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workplaces"]}}, "response": []}, {"name": "Delete User Workplace", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workplaces", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workplaces"]}}, "response": []}, {"name": "Get User Workplace Open Tabs count", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/workplaces/openTabs/count", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workplaces", "openTabs", "count"]}}, "response": []}]}, {"name": "Files", "item": [{"name": "Upload file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/home/<USER>/Downloads/PT-ADBGC-JUD-JFO-001-04385_m0002.jpg"}, {"key": "entity", "value": "xico", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/v1/files", "host": ["{{baseUrl}}"], "path": ["api", "v1", "files"]}}, "response": []}, {"name": "List files", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/files?limit=10&offset=0&sortBy=filename&sortOrder=asc&entity=xico", "host": ["{{baseUrl}}"], "path": ["api", "v1", "files"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}, {"key": "sortBy", "value": "filename"}, {"key": "sortOrder", "value": "asc"}, {"key": "entity", "value": "xico"}]}}, "response": []}, {"name": "Download file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/files/f0ae893d-1940-4545-bdba-34a00b475416", "host": ["{{baseUrl}}"], "path": ["api", "v1", "files", "f0ae893d-1940-4545-bdba-34a00b475416"]}}, "response": []}, {"name": "Delete file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/files/f0ae893d-1940-4545-bdba-34a00b475416", "host": ["{{baseUrl}}"], "path": ["api", "v1", "files", "f0ae893d-1940-4545-bdba-34a00b475416"]}}, "response": []}]}]}