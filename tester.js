import { addLead } from "./src/services/leadService";

const lead = {
  id: 1,
  estado: 'a',
  nome: 'pedro',
  email: 'test@test',
  contacto: '914194101',
  cpostal: '123',
  localidade: 'test',
  distrito: 'test',
  concelho: 'test',
  nif: '219129391',
  origem: 'test',
  mensagem: 'awdandoawdawmdoawd test',
  atribuido_a: 1,
  data_atribuicao: '2018-05-08 16:30:01',
  data_fecho: null,
  id_razao_fecho: null,
  razao_fecho: null,
  data_criacao: '2018-05-08 16:30:01',
  integration_gesmat_curso: null,
  integration_gesmat_polo_atribuicao: null,
  integration_gesmat_centro_formacao: null,
  integration_gesmat_autorizacao_rgpd_4: true,
  integration_gesmat_autorizacao_rgpd_5: true,
  integration_gesmat_enviado_gocontact: false,
  integration_gesmat_atribuicao_gocontact: false
};

addLead(lead);